{"version": 3, "sources": ["../src/index.ts", "../src/xai-provider.ts", "../src/xai-chat-settings.ts", "../src/xai-error.ts"], "sourcesContent": ["export { createXai, xai } from './xai-provider';\nexport type { XaiProvider, XaiProviderSettings } from './xai-provider';\nexport type { XaiErrorData } from './xai-error';\n", "import {\n  ImageModelV1,\n  LanguageModelV1,\n  NoSuchModelError,\n  ProviderV1,\n} from '@ai-sdk/provider';\nimport {\n  OpenAICompatibleChatLanguageModel,\n  OpenAICompatibleImageModel,\n  ProviderErrorStructure,\n} from '@ai-sdk/openai-compatible';\nimport {\n  FetchFunction,\n  loadApiKey,\n  withoutTrailingSlash,\n} from '@ai-sdk/provider-utils';\nimport {\n  XaiChatModelId,\n  XaiChatSettings,\n  supportsStructuredOutputs,\n} from './xai-chat-settings';\nimport { XaiImageSettings } from './xai-image-settings';\nimport { XaiImageModelId } from './xai-image-settings';\nimport { XaiErrorData, xaiErrorSchema } from './xai-error';\n\nconst xaiErrorStructure: ProviderErrorStructure<XaiErrorData> = {\n  errorSchema: xaiErrorSchema,\n  errorToMessage: data => data.error,\n};\n\nexport interface XaiProvider extends ProviderV1 {\n  /**\nCreates an Xai chat model for text generation.\n   */\n  (modelId: XaiChatModelId, settings?: XaiChatSettings): LanguageModelV1;\n\n  /**\nCreates an Xai language model for text generation.\n   */\n  languageModel(\n    modelId: XaiChatModelId,\n    settings?: XaiChatSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates an Xai chat model for text generation.\n   */\n  chat: (\n    modelId: XaiChatModelId,\n    settings?: XaiChatSettings,\n  ) => LanguageModelV1;\n\n  /**\nCreates an Xai image model for image generation.\n   */\n  image(modelId: XaiImageModelId, settings?: XaiImageSettings): ImageModelV1;\n\n  /**\nCreates an Xai image model for image generation.\n   */\n  imageModel(\n    modelId: XaiImageModelId,\n    settings?: XaiImageSettings,\n  ): ImageModelV1;\n}\n\nexport interface XaiProviderSettings {\n  /**\nBase URL for the xAI API calls.\n     */\n  baseURL?: string;\n\n  /**\nAPI key for authenticating requests.\n   */\n  apiKey?: string;\n\n  /**\nCustom headers to include in the requests.\n   */\n  headers?: Record<string, string>;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n  */\n  fetch?: FetchFunction;\n}\n\nexport function createXai(options: XaiProviderSettings = {}): XaiProvider {\n  const baseURL = withoutTrailingSlash(\n    options.baseURL ?? 'https://api.x.ai/v1',\n  );\n  const getHeaders = () => ({\n    Authorization: `Bearer ${loadApiKey({\n      apiKey: options.apiKey,\n      environmentVariableName: 'XAI_API_KEY',\n      description: 'xAI API key',\n    })}`,\n    ...options.headers,\n  });\n\n  const createLanguageModel = (\n    modelId: XaiChatModelId,\n    settings: XaiChatSettings = {},\n  ) => {\n    const structuredOutputs = supportsStructuredOutputs(modelId);\n    return new OpenAICompatibleChatLanguageModel(modelId, settings, {\n      provider: 'xai.chat',\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch,\n      defaultObjectGenerationMode: structuredOutputs ? 'json' : 'tool',\n      errorStructure: xaiErrorStructure,\n      supportsStructuredOutputs: structuredOutputs,\n      includeUsage: true,\n    });\n  };\n\n  const createImageModel = (\n    modelId: XaiImageModelId,\n    settings: XaiImageSettings = {},\n  ) => {\n    return new OpenAICompatibleImageModel(modelId, settings, {\n      provider: 'xai.image',\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch,\n      errorStructure: xaiErrorStructure,\n    });\n  };\n\n  const provider = (modelId: XaiChatModelId, settings?: XaiChatSettings) =>\n    createLanguageModel(modelId, settings);\n\n  provider.languageModel = createLanguageModel;\n  provider.chat = createLanguageModel;\n  provider.textEmbeddingModel = (modelId: string) => {\n    throw new NoSuchModelError({ modelId, modelType: 'textEmbeddingModel' });\n  };\n  provider.imageModel = createImageModel;\n  provider.image = createImageModel;\n\n  return provider;\n}\n\nexport const xai = createXai();\n", "import { OpenAICompatibleChatSettings } from '@ai-sdk/openai-compatible';\n\n// https://console.x.ai and see \"View models\"\nexport type XaiChatModelId =\n  | 'grok-3'\n  | 'grok-3-latest'\n  | 'grok-3-fast'\n  | 'grok-3-fast-latest'\n  | 'grok-3-mini'\n  | 'grok-3-mini-latest'\n  | 'grok-3-mini-fast'\n  | 'grok-3-mini-fast-latest'\n  | 'grok-2-vision-1212'\n  | 'grok-2-vision'\n  | 'grok-2-vision-latest'\n  | 'grok-2-image-1212'\n  | 'grok-2-image'\n  | 'grok-2-image-latest'\n  | 'grok-2-1212'\n  | 'grok-2'\n  | 'grok-2-latest'\n  | 'grok-vision-beta'\n  | 'grok-beta'\n  | (string & {});\n\nexport interface XaiChatSettings extends OpenAICompatibleChatSettings {}\n\n/**\n * https://docs.x.ai/docs/guides/structured-outputs\n */\nexport function supportsStructuredOutputs(modelId: XaiChatModelId) {\n  return [\n    'grok-3',\n    'grok-3-beta',\n    'grok-3-latest',\n    'grok-3-fast',\n    'grok-3-fast-beta',\n    'grok-3-fast-latest',\n    'grok-3-mini',\n    'grok-3-mini-beta',\n    'grok-3-mini-latest',\n    'grok-3-mini-fast',\n    'grok-3-mini-fast-beta',\n    'grok-3-mini-fast-latest',\n    'grok-2-1212',\n    'grok-2-vision-1212',\n  ].includes(modelId);\n}\n", "import { z } from 'zod';\n\n// Add error schema and structure\nexport const xaiErrorSchema = z.object({\n  code: z.string(),\n  error: z.string(),\n});\n\nexport type XaiErrorData = z.infer<typeof xaiErrorSchema>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,sBAKO;AACP,+BAIO;AACP,4BAIO;;;ACeA,SAAS,0BAA0B,SAAyB;AACjE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,EAAE,SAAS,OAAO;AACpB;;;AC/CA,iBAAkB;AAGX,IAAM,iBAAiB,aAAE,OAAO;AAAA,EACrC,MAAM,aAAE,OAAO;AAAA,EACf,OAAO,aAAE,OAAO;AAClB,CAAC;;;AFmBD,IAAM,oBAA0D;AAAA,EAC9D,aAAa;AAAA,EACb,gBAAgB,UAAQ,KAAK;AAC/B;AA6DO,SAAS,UAAU,UAA+B,CAAC,GAAgB;AAzF1E;AA0FE,QAAM,cAAU;AAAA,KACd,aAAQ,YAAR,YAAmB;AAAA,EACrB;AACA,QAAM,aAAa,OAAO;AAAA,IACxB,eAAe,cAAU,kCAAW;AAAA,MAClC,QAAQ,QAAQ;AAAA,MAChB,yBAAyB;AAAA,MACzB,aAAa;AAAA,IACf,CAAC,CAAC;AAAA,IACF,GAAG,QAAQ;AAAA,EACb;AAEA,QAAM,sBAAsB,CAC1B,SACA,WAA4B,CAAC,MAC1B;AACH,UAAM,oBAAoB,0BAA0B,OAAO;AAC3D,WAAO,IAAI,2DAAkC,SAAS,UAAU;AAAA,MAC9D,UAAU;AAAA,MACV,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,OAAO,GAAG,IAAI;AAAA,MACpC,SAAS;AAAA,MACT,OAAO,QAAQ;AAAA,MACf,6BAA6B,oBAAoB,SAAS;AAAA,MAC1D,gBAAgB;AAAA,MAChB,2BAA2B;AAAA,MAC3B,cAAc;AAAA,IAChB,CAAC;AAAA,EACH;AAEA,QAAM,mBAAmB,CACvB,SACA,WAA6B,CAAC,MAC3B;AACH,WAAO,IAAI,oDAA2B,SAAS,UAAU;AAAA,MACvD,UAAU;AAAA,MACV,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,OAAO,GAAG,IAAI;AAAA,MACpC,SAAS;AAAA,MACT,OAAO,QAAQ;AAAA,MACf,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAEA,QAAM,WAAW,CAAC,SAAyB,aACzC,oBAAoB,SAAS,QAAQ;AAEvC,WAAS,gBAAgB;AACzB,WAAS,OAAO;AAChB,WAAS,qBAAqB,CAAC,YAAoB;AACjD,UAAM,IAAI,iCAAiB,EAAE,SAAS,WAAW,qBAAqB,CAAC;AAAA,EACzE;AACA,WAAS,aAAa;AACtB,WAAS,QAAQ;AAEjB,SAAO;AACT;AAEO,IAAM,MAAM,UAAU;", "names": []}
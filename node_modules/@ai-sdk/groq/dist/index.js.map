{"version": 3, "sources": ["../src/index.ts", "../src/groq-provider.ts", "../src/groq-chat-language-model.ts", "../src/convert-to-groq-chat-messages.ts", "../src/get-response-metadata.ts", "../src/groq-error.ts", "../src/groq-prepare-tools.ts", "../src/map-groq-finish-reason.ts", "../src/groq-transcription-model.ts"], "sourcesContent": ["export { createGroq, groq } from './groq-provider';\nexport type { GroqProvider, GroqProviderSettings } from './groq-provider';\n", "import {\n  LanguageModelV1,\n  NoSuchModelError,\n  ProviderV1,\n  TranscriptionModelV1,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  loadApiKey,\n  withoutTrailingSlash,\n} from '@ai-sdk/provider-utils';\nimport { GroqChatLanguageModel } from './groq-chat-language-model';\nimport { GroqChatModelId, GroqChatSettings } from './groq-chat-settings';\nimport { GroqTranscriptionModelId } from './groq-transcription-settings';\nimport { GroqTranscriptionModel } from './groq-transcription-model';\n\nexport interface GroqProvider extends ProviderV1 {\n  /**\nCreates a model for text generation.\n*/\n  (modelId: GroqChatModelId, settings?: GroqChatSettings): LanguageModelV1;\n\n  /**\nCreates an Groq chat model for text generation.\n   */\n  languageModel(\n    modelId: GroqChatModelId,\n    settings?: GroqChatSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates a model for transcription.\n   */\n  transcription(modelId: GroqTranscriptionModelId): TranscriptionModelV1;\n}\n\nexport interface GroqProviderSettings {\n  /**\nBase URL for the Groq API calls.\n     */\n  baseURL?: string;\n\n  /**\nAPI key for authenticating requests.\n     */\n  apiKey?: string;\n\n  /**\nCustom headers to include in the requests.\n     */\n  headers?: Record<string, string>;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n    */\n  fetch?: FetchFunction;\n}\n\n/**\nCreate an Groq provider instance.\n */\nexport function createGroq(options: GroqProviderSettings = {}): GroqProvider {\n  const baseURL =\n    withoutTrailingSlash(options.baseURL) ?? 'https://api.groq.com/openai/v1';\n\n  const getHeaders = () => ({\n    Authorization: `Bearer ${loadApiKey({\n      apiKey: options.apiKey,\n      environmentVariableName: 'GROQ_API_KEY',\n      description: 'Groq',\n    })}`,\n    ...options.headers,\n  });\n\n  const createChatModel = (\n    modelId: GroqChatModelId,\n    settings: GroqChatSettings = {},\n  ) =>\n    new GroqChatLanguageModel(modelId, settings, {\n      provider: 'groq.chat',\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n\n  const createLanguageModel = (\n    modelId: GroqChatModelId,\n    settings?: GroqChatSettings,\n  ) => {\n    if (new.target) {\n      throw new Error(\n        'The Groq model function cannot be called with the new keyword.',\n      );\n    }\n\n    return createChatModel(modelId, settings);\n  };\n\n  const createTranscriptionModel = (modelId: GroqTranscriptionModelId) => {\n    return new GroqTranscriptionModel(modelId, {\n      provider: 'groq.transcription',\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n  };\n\n  const provider = function (\n    modelId: GroqChatModelId,\n    settings?: GroqChatSettings,\n  ) {\n    return createLanguageModel(modelId, settings);\n  };\n\n  provider.languageModel = createLanguageModel;\n  provider.chat = createChatModel;\n  provider.textEmbeddingModel = (modelId: string) => {\n    throw new NoSuchModelError({ modelId, modelType: 'textEmbeddingModel' });\n  };\n  provider.transcription = createTranscriptionModel;\n\n  return provider;\n}\n\n/**\nDefault Groq provider instance.\n */\nexport const groq = createGroq();\n", "import {\n  InvalidResponseDataError,\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  LanguageModelV1FinishReason,\n  LanguageModelV1ProviderMetadata,\n  LanguageModelV1StreamPart,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  ParseResult,\n  combineHeaders,\n  createEventSourceResponseHandler,\n  createJsonResponseHandler,\n  generateId,\n  isParsableJson,\n  parseProviderOptions,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { convertToGroqChatMessages } from './convert-to-groq-chat-messages';\nimport { getResponseMetadata } from './get-response-metadata';\nimport { GroqChatModelId, GroqChatSettings } from './groq-chat-settings';\nimport { groqErrorDataSchema, groqFailedResponseHandler } from './groq-error';\nimport { prepareTools } from './groq-prepare-tools';\nimport { mapGroqFinishReason } from './map-groq-finish-reason';\n\ntype GroqChatConfig = {\n  provider: string;\n  headers: () => Record<string, string | undefined>;\n  url: (options: { modelId: string; path: string }) => string;\n  fetch?: FetchFunction;\n};\n\nexport class GroqChatLanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = 'v1';\n\n  readonly supportsStructuredOutputs = false;\n  readonly defaultObjectGenerationMode = 'json';\n\n  readonly modelId: GroqChatModelId;\n  readonly settings: GroqChatSettings;\n\n  private readonly config: GroqChatConfig;\n\n  constructor(\n    modelId: GroqChatModelId,\n    settings: GroqChatSettings,\n    config: GroqChatConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  get supportsImageUrls(): boolean {\n    // image urls can be sent if downloadImages is disabled (default):\n    return !this.settings.downloadImages;\n  }\n\n  private getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n    stream,\n    providerMetadata,\n  }: Parameters<LanguageModelV1['doGenerate']>[0] & {\n    stream: boolean;\n  }) {\n    const type = mode.type;\n\n    const warnings: LanguageModelV1CallWarning[] = [];\n\n    if (topK != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'topK',\n      });\n    }\n\n    if (\n      responseFormat != null &&\n      responseFormat.type === 'json' &&\n      responseFormat.schema != null\n    ) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'responseFormat',\n        details: 'JSON response format schema is not supported',\n      });\n    }\n\n    const groqOptions = parseProviderOptions({\n      provider: 'groq',\n      providerOptions: providerMetadata,\n      schema: z.object({\n        reasoningFormat: z.enum(['parsed', 'raw', 'hidden']).nullish(),\n      }),\n    });\n\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n\n      // model specific settings:\n      user: this.settings.user,\n      parallel_tool_calls: this.settings.parallelToolCalls,\n\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      stop: stopSequences,\n      seed,\n\n      // response format:\n      response_format:\n        // json object response format is not supported for streaming:\n        stream === false && responseFormat?.type === 'json'\n          ? { type: 'json_object' }\n          : undefined,\n\n      // provider options:\n      reasoning_format: groqOptions?.reasoningFormat,\n\n      // messages:\n      messages: convertToGroqChatMessages(prompt),\n    };\n\n    switch (type) {\n      case 'regular': {\n        const { tools, tool_choice, toolWarnings } = prepareTools({ mode });\n        return {\n          args: {\n            ...baseArgs,\n            tools,\n            tool_choice,\n          },\n          warnings: [...warnings, ...toolWarnings],\n        };\n      }\n\n      case 'object-json': {\n        return {\n          args: {\n            ...baseArgs,\n            response_format:\n              // json object response format is not supported for streaming:\n              stream === false ? { type: 'json_object' } : undefined,\n          },\n          warnings,\n        };\n      }\n\n      case 'object-tool': {\n        return {\n          args: {\n            ...baseArgs,\n            tool_choice: {\n              type: 'function',\n              function: { name: mode.tool.name },\n            },\n            tools: [\n              {\n                type: 'function',\n                function: {\n                  name: mode.tool.name,\n                  description: mode.tool.description,\n                  parameters: mode.tool.parameters,\n                },\n              },\n            ],\n          },\n          warnings,\n        };\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>> {\n    const { args, warnings } = this.getArgs({ ...options, stream: false });\n\n    const body = JSON.stringify(args);\n\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse,\n    } = await postJsonToApi({\n      url: this.config.url({\n        path: '/chat/completions',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: groqFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        groqChatResponseSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { messages: rawPrompt, ...rawSettings } = args;\n    const choice = response.choices[0];\n\n    return {\n      text: choice.message.content ?? undefined,\n      reasoning: choice.message.reasoning ?? undefined,\n      toolCalls: choice.message.tool_calls?.map(toolCall => ({\n        toolCallType: 'function',\n        toolCallId: toolCall.id ?? generateId(),\n        toolName: toolCall.function.name,\n        args: toolCall.function.arguments!,\n      })),\n      finishReason: mapGroqFinishReason(choice.finish_reason),\n      usage: {\n        promptTokens: response.usage?.prompt_tokens ?? NaN,\n        completionTokens: response.usage?.completion_tokens ?? NaN,\n      },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders, body: rawResponse },\n      response: getResponseMetadata(response),\n      warnings,\n      request: { body },\n    };\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1['doStream']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>> {\n    const { args, warnings } = this.getArgs({ ...options, stream: true });\n\n    const body = JSON.stringify({ ...args, stream: true });\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: this.config.url({\n        path: '/chat/completions',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: {\n        ...args,\n        stream: true,\n      },\n      failedResponseHandler: groqFailedResponseHandler,\n      successfulResponseHandler:\n        createEventSourceResponseHandler(groqChatChunkSchema),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { messages: rawPrompt, ...rawSettings } = args;\n\n    const toolCalls: Array<{\n      id: string;\n      type: 'function';\n      function: {\n        name: string;\n        arguments: string;\n      };\n      hasFinished: boolean;\n    }> = [];\n\n    let finishReason: LanguageModelV1FinishReason = 'unknown';\n    let usage: {\n      promptTokens: number | undefined;\n      completionTokens: number | undefined;\n    } = {\n      promptTokens: undefined,\n      completionTokens: undefined,\n    };\n    let isFirstChunk = true;\n\n    let providerMetadata: LanguageModelV1ProviderMetadata | undefined;\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof groqChatChunkSchema>>,\n          LanguageModelV1StreamPart\n        >({\n          transform(chunk, controller) {\n            // handle failed chunk parsing / validation:\n            if (!chunk.success) {\n              finishReason = 'error';\n              controller.enqueue({ type: 'error', error: chunk.error });\n              return;\n            }\n\n            const value = chunk.value;\n\n            // handle error chunks:\n            if ('error' in value) {\n              finishReason = 'error';\n              controller.enqueue({ type: 'error', error: value.error });\n              return;\n            }\n\n            if (isFirstChunk) {\n              isFirstChunk = false;\n\n              controller.enqueue({\n                type: 'response-metadata',\n                ...getResponseMetadata(value),\n              });\n            }\n\n            if (value.x_groq?.usage != null) {\n              usage = {\n                promptTokens: value.x_groq.usage.prompt_tokens ?? undefined,\n                completionTokens:\n                  value.x_groq.usage.completion_tokens ?? undefined,\n              };\n            }\n\n            const choice = value.choices[0];\n\n            if (choice?.finish_reason != null) {\n              finishReason = mapGroqFinishReason(choice.finish_reason);\n            }\n\n            if (choice?.delta == null) {\n              return;\n            }\n\n            const delta = choice.delta;\n\n            if (delta.reasoning != null && delta.reasoning.length > 0) {\n              controller.enqueue({\n                type: 'reasoning',\n                textDelta: delta.reasoning,\n              });\n            }\n\n            if (delta.content != null && delta.content.length > 0) {\n              controller.enqueue({\n                type: 'text-delta',\n                textDelta: delta.content,\n              });\n            }\n\n            if (delta.tool_calls != null) {\n              for (const toolCallDelta of delta.tool_calls) {\n                const index = toolCallDelta.index;\n\n                if (toolCalls[index] == null) {\n                  if (toolCallDelta.type !== 'function') {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function' type.`,\n                    });\n                  }\n\n                  if (toolCallDelta.id == null) {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'id' to be a string.`,\n                    });\n                  }\n\n                  if (toolCallDelta.function?.name == null) {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function.name' to be a string.`,\n                    });\n                  }\n\n                  toolCalls[index] = {\n                    id: toolCallDelta.id,\n                    type: 'function',\n                    function: {\n                      name: toolCallDelta.function.name,\n                      arguments: toolCallDelta.function.arguments ?? '',\n                    },\n                    hasFinished: false,\n                  };\n\n                  const toolCall = toolCalls[index];\n\n                  if (\n                    toolCall.function?.name != null &&\n                    toolCall.function?.arguments != null\n                  ) {\n                    // send delta if the argument text has already started:\n                    if (toolCall.function.arguments.length > 0) {\n                      controller.enqueue({\n                        type: 'tool-call-delta',\n                        toolCallType: 'function',\n                        toolCallId: toolCall.id,\n                        toolName: toolCall.function.name,\n                        argsTextDelta: toolCall.function.arguments,\n                      });\n                    }\n\n                    // check if tool call is complete\n                    // (some providers send the full tool call in one chunk):\n                    if (isParsableJson(toolCall.function.arguments)) {\n                      controller.enqueue({\n                        type: 'tool-call',\n                        toolCallType: 'function',\n                        toolCallId: toolCall.id ?? generateId(),\n                        toolName: toolCall.function.name,\n                        args: toolCall.function.arguments,\n                      });\n                      toolCall.hasFinished = true;\n                    }\n                  }\n\n                  continue;\n                }\n\n                // existing tool call, merge if not finished\n                const toolCall = toolCalls[index];\n\n                if (toolCall.hasFinished) {\n                  continue;\n                }\n\n                if (toolCallDelta.function?.arguments != null) {\n                  toolCall.function!.arguments +=\n                    toolCallDelta.function?.arguments ?? '';\n                }\n\n                // send delta\n                controller.enqueue({\n                  type: 'tool-call-delta',\n                  toolCallType: 'function',\n                  toolCallId: toolCall.id,\n                  toolName: toolCall.function.name,\n                  argsTextDelta: toolCallDelta.function.arguments ?? '',\n                });\n\n                // check if tool call is complete\n                if (\n                  toolCall.function?.name != null &&\n                  toolCall.function?.arguments != null &&\n                  isParsableJson(toolCall.function.arguments)\n                ) {\n                  controller.enqueue({\n                    type: 'tool-call',\n                    toolCallType: 'function',\n                    toolCallId: toolCall.id ?? generateId(),\n                    toolName: toolCall.function.name,\n                    args: toolCall.function.arguments,\n                  });\n                  toolCall.hasFinished = true;\n                }\n              }\n            }\n          },\n\n          flush(controller) {\n            controller.enqueue({\n              type: 'finish',\n              finishReason,\n              usage: {\n                promptTokens: usage.promptTokens ?? NaN,\n                completionTokens: usage.completionTokens ?? NaN,\n              },\n              ...(providerMetadata != null ? { providerMetadata } : {}),\n            });\n          },\n        }),\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n      request: { body },\n    };\n  }\n}\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst groqChatResponseSchema = z.object({\n  id: z.string().nullish(),\n  created: z.number().nullish(),\n  model: z.string().nullish(),\n  choices: z.array(\n    z.object({\n      message: z.object({\n        content: z.string().nullish(),\n        reasoning: z.string().nullish(),\n        tool_calls: z\n          .array(\n            z.object({\n              id: z.string().nullish(),\n              type: z.literal('function'),\n              function: z.object({\n                name: z.string(),\n                arguments: z.string(),\n              }),\n            }),\n          )\n          .nullish(),\n      }),\n      index: z.number(),\n      finish_reason: z.string().nullish(),\n    }),\n  ),\n  usage: z\n    .object({\n      prompt_tokens: z.number().nullish(),\n      completion_tokens: z.number().nullish(),\n    })\n    .nullish(),\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst groqChatChunkSchema = z.union([\n  z.object({\n    id: z.string().nullish(),\n    created: z.number().nullish(),\n    model: z.string().nullish(),\n    choices: z.array(\n      z.object({\n        delta: z\n          .object({\n            content: z.string().nullish(),\n            reasoning: z.string().nullish(),\n            tool_calls: z\n              .array(\n                z.object({\n                  index: z.number(),\n                  id: z.string().nullish(),\n                  type: z.literal('function').optional(),\n                  function: z.object({\n                    name: z.string().nullish(),\n                    arguments: z.string().nullish(),\n                  }),\n                }),\n              )\n              .nullish(),\n          })\n          .nullish(),\n        finish_reason: z.string().nullable().optional(),\n        index: z.number(),\n      }),\n    ),\n    x_groq: z\n      .object({\n        usage: z\n          .object({\n            prompt_tokens: z.number().nullish(),\n            completion_tokens: z.number().nullish(),\n          })\n          .nullish(),\n      })\n      .nullish(),\n  }),\n  groqErrorDataSchema,\n]);\n", "import {\n  LanguageModelV1Prompt,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { convertUint8ArrayToBase64 } from '@ai-sdk/provider-utils';\nimport { GroqChatPrompt } from './groq-api-types';\n\nexport function convertToGroqChatMessages(\n  prompt: LanguageModelV1Prompt,\n): GroqChatPrompt {\n  const messages: GroqChatPrompt = [];\n\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case 'system': {\n        messages.push({ role: 'system', content });\n        break;\n      }\n\n      case 'user': {\n        if (content.length === 1 && content[0].type === 'text') {\n          messages.push({ role: 'user', content: content[0].text });\n          break;\n        }\n\n        messages.push({\n          role: 'user',\n          content: content.map(part => {\n            switch (part.type) {\n              case 'text': {\n                return { type: 'text', text: part.text };\n              }\n              case 'image': {\n                return {\n                  type: 'image_url',\n                  image_url: {\n                    url:\n                      part.image instanceof URL\n                        ? part.image.toString()\n                        : `data:${\n                            part.mimeType ?? 'image/jpeg'\n                          };base64,${convertUint8ArrayToBase64(part.image)}`,\n                  },\n                };\n              }\n              case 'file': {\n                throw new UnsupportedFunctionalityError({\n                  functionality: 'File content parts in user messages',\n                });\n              }\n            }\n          }),\n        });\n\n        break;\n      }\n\n      case 'assistant': {\n        let text = '';\n        const toolCalls: Array<{\n          id: string;\n          type: 'function';\n          function: { name: string; arguments: string };\n        }> = [];\n\n        for (const part of content) {\n          switch (part.type) {\n            case 'text': {\n              text += part.text;\n              break;\n            }\n            case 'tool-call': {\n              toolCalls.push({\n                id: part.toolCallId,\n                type: 'function',\n                function: {\n                  name: part.toolName,\n                  arguments: JSON.stringify(part.args),\n                },\n              });\n              break;\n            }\n          }\n        }\n\n        messages.push({\n          role: 'assistant',\n          content: text,\n          tool_calls: toolCalls.length > 0 ? toolCalls : undefined,\n        });\n\n        break;\n      }\n\n      case 'tool': {\n        for (const toolResponse of content) {\n          messages.push({\n            role: 'tool',\n            tool_call_id: toolResponse.toolCallId,\n            content: JSON.stringify(toolResponse.result),\n          });\n        }\n        break;\n      }\n\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return messages;\n}\n", "export function getResponseMetadata({\n  id,\n  model,\n  created,\n}: {\n  id?: string | undefined | null;\n  created?: number | undefined | null;\n  model?: string | undefined | null;\n}) {\n  return {\n    id: id ?? undefined,\n    modelId: model ?? undefined,\n    timestamp: created != null ? new Date(created * 1000) : undefined,\n  };\n}\n", "import { z } from 'zod';\nimport { createJsonErrorResponseHandler } from '@ai-sdk/provider-utils';\n\nexport const groqErrorDataSchema = z.object({\n  error: z.object({\n    message: z.string(),\n    type: z.string(),\n  }),\n});\n\nexport type GroqErrorData = z.infer<typeof groqErrorDataSchema>;\n\nexport const groqFailedResponseHandler = createJsonErrorResponseHandler({\n  errorSchema: groqErrorDataSchema,\n  errorToMessage: data => data.error.message,\n});\n", "import {\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\n\nexport function prepareTools({\n  mode,\n}: {\n  mode: Parameters<LanguageModelV1['doGenerate']>[0]['mode'] & {\n    type: 'regular';\n  };\n}): {\n  tools:\n    | undefined\n    | Array<{\n        type: 'function';\n        function: {\n          name: string;\n          description: string | undefined;\n          parameters: unknown;\n        };\n      }>;\n  tool_choice:\n    | { type: 'function'; function: { name: string } }\n    | 'auto'\n    | 'none'\n    | 'required'\n    | undefined;\n  toolWarnings: LanguageModelV1CallWarning[];\n} {\n  // when the tools array is empty, change it to undefined to prevent errors:\n  const tools = mode.tools?.length ? mode.tools : undefined;\n  const toolWarnings: LanguageModelV1CallWarning[] = [];\n\n  if (tools == null) {\n    return { tools: undefined, tool_choice: undefined, toolWarnings };\n  }\n\n  const toolChoice = mode.toolChoice;\n\n  const groqTools: Array<{\n    type: 'function';\n    function: {\n      name: string;\n      description: string | undefined;\n      parameters: unknown;\n    };\n  }> = [];\n\n  for (const tool of tools) {\n    if (tool.type === 'provider-defined') {\n      toolWarnings.push({ type: 'unsupported-tool', tool });\n    } else {\n      groqTools.push({\n        type: 'function',\n        function: {\n          name: tool.name,\n          description: tool.description,\n          parameters: tool.parameters,\n        },\n      });\n    }\n  }\n\n  if (toolChoice == null) {\n    return { tools: groqTools, tool_choice: undefined, toolWarnings };\n  }\n\n  const type = toolChoice.type;\n\n  switch (type) {\n    case 'auto':\n    case 'none':\n    case 'required':\n      return { tools: groqTools, tool_choice: type, toolWarnings };\n    case 'tool':\n      return {\n        tools: groqTools,\n        tool_choice: {\n          type: 'function',\n          function: {\n            name: toolChoice.toolName,\n          },\n        },\n        toolWarnings,\n      };\n    default: {\n      const _exhaustiveCheck: never = type;\n      throw new UnsupportedFunctionalityError({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`,\n      });\n    }\n  }\n}\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\n\nexport function mapGroqFinishReason(\n  finishReason: string | null | undefined,\n): LanguageModelV1FinishReason {\n  switch (finishReason) {\n    case 'stop':\n      return 'stop';\n    case 'length':\n      return 'length';\n    case 'content_filter':\n      return 'content-filter';\n    case 'function_call':\n    case 'tool_calls':\n      return 'tool-calls';\n    default:\n      return 'unknown';\n  }\n}\n", "import {\n  TranscriptionModelV1,\n  TranscriptionModelV1CallWarning,\n} from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  convertBase64ToUint8Array,\n  createJsonResponseHandler,\n  parseProviderOptions,\n  postFormDataToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { GroqConfig } from './groq-config';\nimport { groqFailedResponseHandler } from './groq-error';\nimport { GroqTranscriptionModelId } from './groq-transcription-settings';\nimport { GroqTranscriptionAPITypes } from './groq-api-types';\n\n// https://console.groq.com/docs/speech-to-text\nconst groqProviderOptionsSchema = z.object({\n  language: z.string().nullish(),\n  prompt: z.string().nullish(),\n  responseFormat: z.string().nullish(),\n  temperature: z.number().min(0).max(1).nullish(),\n  timestampGranularities: z.array(z.string()).nullish(),\n});\n\nexport type GroqTranscriptionCallOptions = z.infer<\n  typeof groqProviderOptionsSchema\n>;\n\ninterface GroqTranscriptionModelConfig extends GroqConfig {\n  _internal?: {\n    currentDate?: () => Date;\n  };\n}\n\nexport class GroqTranscriptionModel implements TranscriptionModelV1 {\n  readonly specificationVersion = 'v1';\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  constructor(\n    readonly modelId: GroqTranscriptionModelId,\n    private readonly config: GroqTranscriptionModelConfig,\n  ) {}\n\n  private getArgs({\n    audio,\n    mediaType,\n    providerOptions,\n  }: Parameters<TranscriptionModelV1['doGenerate']>[0]) {\n    const warnings: TranscriptionModelV1CallWarning[] = [];\n\n    // Parse provider options\n    const groqOptions = parseProviderOptions({\n      provider: 'groq',\n      providerOptions,\n      schema: groqProviderOptionsSchema,\n    });\n\n    // Create form data with base fields\n    const formData = new FormData();\n    const blob =\n      audio instanceof Uint8Array\n        ? new Blob([audio])\n        : new Blob([convertBase64ToUint8Array(audio)]);\n\n    formData.append('model', this.modelId);\n    formData.append('file', new File([blob], 'audio', { type: mediaType }));\n\n    // Add provider-specific options\n    if (groqOptions) {\n      const transcriptionModelOptions: Omit<\n        GroqTranscriptionAPITypes,\n        'model'\n      > = {\n        language: groqOptions.language ?? undefined,\n        prompt: groqOptions.prompt ?? undefined,\n        response_format: groqOptions.responseFormat ?? undefined,\n        temperature: groqOptions.temperature ?? undefined,\n        timestamp_granularities:\n          groqOptions.timestampGranularities ?? undefined,\n      };\n\n      for (const key in transcriptionModelOptions) {\n        const value =\n          transcriptionModelOptions[\n            key as keyof Omit<GroqTranscriptionAPITypes, 'model'>\n          ];\n        if (value !== undefined) {\n          formData.append(key, String(value));\n        }\n      }\n    }\n\n    return {\n      formData,\n      warnings,\n    };\n  }\n\n  async doGenerate(\n    options: Parameters<TranscriptionModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<TranscriptionModelV1['doGenerate']>>> {\n    const currentDate = this.config._internal?.currentDate?.() ?? new Date();\n    const { formData, warnings } = this.getArgs(options);\n\n    const {\n      value: response,\n      responseHeaders,\n      rawValue: rawResponse,\n    } = await postFormDataToApi({\n      url: this.config.url({\n        path: '/audio/transcriptions',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      formData,\n      failedResponseHandler: groqFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        groqTranscriptionResponseSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    return {\n      text: response.text,\n      segments:\n        response.segments?.map(segment => ({\n          text: segment.text,\n          startSecond: segment.start,\n          endSecond: segment.end,\n        })) ?? [],\n      language: response.language,\n      durationInSeconds: response.duration,\n      warnings,\n      response: {\n        timestamp: currentDate,\n        modelId: this.modelId,\n        headers: responseHeaders,\n        body: rawResponse,\n      },\n    };\n  }\n}\n\nconst groqTranscriptionResponseSchema = z.object({\n  task: z.string(),\n  language: z.string(),\n  duration: z.number(),\n  text: z.string(),\n  segments: z.array(\n    z.object({\n      id: z.number(),\n      seek: z.number(),\n      start: z.number(),\n      end: z.number(),\n      text: z.string(),\n      tokens: z.array(z.number()),\n      temperature: z.number(),\n      avg_logprob: z.number(),\n      compression_ratio: z.number(),\n      no_speech_prob: z.number(),\n    }),\n  ),\n  x_groq: z.object({\n    id: z.string(),\n  }),\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAA,mBAKO;AACP,IAAAC,yBAIO;;;ACVP,IAAAC,mBAOO;AACP,IAAAC,yBAUO;AACP,IAAAC,cAAkB;;;ACnBlB,sBAGO;AACP,4BAA0C;AAGnC,SAAS,0BACd,QACgB;AAChB,QAAM,WAA2B,CAAC;AAElC,aAAW,EAAE,MAAM,QAAQ,KAAK,QAAQ;AACtC,YAAQ,MAAM;AAAA,MACZ,KAAK,UAAU;AACb,iBAAS,KAAK,EAAE,MAAM,UAAU,QAAQ,CAAC;AACzC;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AACX,YAAI,QAAQ,WAAW,KAAK,QAAQ,CAAC,EAAE,SAAS,QAAQ;AACtD,mBAAS,KAAK,EAAE,MAAM,QAAQ,SAAS,QAAQ,CAAC,EAAE,KAAK,CAAC;AACxD;AAAA,QACF;AAEA,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,SAAS,QAAQ,IAAI,UAAQ;AA3BvC;AA4BY,oBAAQ,KAAK,MAAM;AAAA,cACjB,KAAK,QAAQ;AACX,uBAAO,EAAE,MAAM,QAAQ,MAAM,KAAK,KAAK;AAAA,cACzC;AAAA,cACA,KAAK,SAAS;AACZ,uBAAO;AAAA,kBACL,MAAM;AAAA,kBACN,WAAW;AAAA,oBACT,KACE,KAAK,iBAAiB,MAClB,KAAK,MAAM,SAAS,IACpB,SACE,UAAK,aAAL,YAAiB,YACnB,eAAW,iDAA0B,KAAK,KAAK,CAAC;AAAA,kBACxD;AAAA,gBACF;AAAA,cACF;AAAA,cACA,KAAK,QAAQ;AACX,sBAAM,IAAI,8CAA8B;AAAA,kBACtC,eAAe;AAAA,gBACjB,CAAC;AAAA,cACH;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAED;AAAA,MACF;AAAA,MAEA,KAAK,aAAa;AAChB,YAAI,OAAO;AACX,cAAM,YAID,CAAC;AAEN,mBAAW,QAAQ,SAAS;AAC1B,kBAAQ,KAAK,MAAM;AAAA,YACjB,KAAK,QAAQ;AACX,sBAAQ,KAAK;AACb;AAAA,YACF;AAAA,YACA,KAAK,aAAa;AAChB,wBAAU,KAAK;AAAA,gBACb,IAAI,KAAK;AAAA,gBACT,MAAM;AAAA,gBACN,UAAU;AAAA,kBACR,MAAM,KAAK;AAAA,kBACX,WAAW,KAAK,UAAU,KAAK,IAAI;AAAA,gBACrC;AAAA,cACF,CAAC;AACD;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,SAAS;AAAA,UACT,YAAY,UAAU,SAAS,IAAI,YAAY;AAAA,QACjD,CAAC;AAED;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AACX,mBAAW,gBAAgB,SAAS;AAClC,mBAAS,KAAK;AAAA,YACZ,MAAM;AAAA,YACN,cAAc,aAAa;AAAA,YAC3B,SAAS,KAAK,UAAU,aAAa,MAAM;AAAA,UAC7C,CAAC;AAAA,QACH;AACA;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;ACjHO,SAAS,oBAAoB;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AACF,GAIG;AACD,SAAO;AAAA,IACL,IAAI,kBAAM;AAAA,IACV,SAAS,wBAAS;AAAA,IAClB,WAAW,WAAW,OAAO,IAAI,KAAK,UAAU,GAAI,IAAI;AAAA,EAC1D;AACF;;;ACdA,iBAAkB;AAClB,IAAAC,yBAA+C;AAExC,IAAM,sBAAsB,aAAE,OAAO;AAAA,EAC1C,OAAO,aAAE,OAAO;AAAA,IACd,SAAS,aAAE,OAAO;AAAA,IAClB,MAAM,aAAE,OAAO;AAAA,EACjB,CAAC;AACH,CAAC;AAIM,IAAM,gCAA4B,uDAA+B;AAAA,EACtE,aAAa;AAAA,EACb,gBAAgB,UAAQ,KAAK,MAAM;AACrC,CAAC;;;ACfD,IAAAC,mBAIO;AAEA,SAAS,aAAa;AAAA,EAC3B;AACF,GAsBE;AA9BF;AAgCE,QAAM,UAAQ,UAAK,UAAL,mBAAY,UAAS,KAAK,QAAQ;AAChD,QAAM,eAA6C,CAAC;AAEpD,MAAI,SAAS,MAAM;AACjB,WAAO,EAAE,OAAO,QAAW,aAAa,QAAW,aAAa;AAAA,EAClE;AAEA,QAAM,aAAa,KAAK;AAExB,QAAM,YAOD,CAAC;AAEN,aAAW,QAAQ,OAAO;AACxB,QAAI,KAAK,SAAS,oBAAoB;AACpC,mBAAa,KAAK,EAAE,MAAM,oBAAoB,KAAK,CAAC;AAAA,IACtD,OAAO;AACL,gBAAU,KAAK;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,UACR,MAAM,KAAK;AAAA,UACX,aAAa,KAAK;AAAA,UAClB,YAAY,KAAK;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,cAAc,MAAM;AACtB,WAAO,EAAE,OAAO,WAAW,aAAa,QAAW,aAAa;AAAA,EAClE;AAEA,QAAM,OAAO,WAAW;AAExB,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,EAAE,OAAO,WAAW,aAAa,MAAM,aAAa;AAAA,IAC7D,KAAK;AACH,aAAO;AAAA,QACL,OAAO;AAAA,QACP,aAAa;AAAA,UACX,MAAM;AAAA,UACN,UAAU;AAAA,YACR,MAAM,WAAW;AAAA,UACnB;AAAA,QACF;AAAA,QACA;AAAA,MACF;AAAA,IACF,SAAS;AACP,YAAM,mBAA0B;AAChC,YAAM,IAAI,+CAA8B;AAAA,QACtC,eAAe,iCAAiC,gBAAgB;AAAA,MAClE,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;AC5FO,SAAS,oBACd,cAC6B;AAC7B,UAAQ,cAAc;AAAA,IACpB,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;;;ALgBO,IAAM,wBAAN,MAAuD;AAAA,EAW5D,YACE,SACA,UACA,QACA;AAdF,SAAS,uBAAuB;AAEhC,SAAS,4BAA4B;AACrC,SAAS,8BAA8B;AAYrC,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAChB;AAAA,EAEA,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EAEA,IAAI,oBAA6B;AAE/B,WAAO,CAAC,KAAK,SAAS;AAAA,EACxB;AAAA,EAEQ,QAAQ;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAEG;AACD,UAAM,OAAO,KAAK;AAElB,UAAM,WAAyC,CAAC;AAEhD,QAAI,QAAQ,MAAM;AAChB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QACE,kBAAkB,QAClB,eAAe,SAAS,UACxB,eAAe,UAAU,MACzB;AACA,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,UAAM,kBAAc,6CAAqB;AAAA,MACvC,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,QAAQ,cAAE,OAAO;AAAA,QACf,iBAAiB,cAAE,KAAK,CAAC,UAAU,OAAO,QAAQ,CAAC,EAAE,QAAQ;AAAA,MAC/D,CAAC;AAAA,IACH,CAAC;AAED,UAAM,WAAW;AAAA;AAAA,MAEf,OAAO,KAAK;AAAA;AAAA,MAGZ,MAAM,KAAK,SAAS;AAAA,MACpB,qBAAqB,KAAK,SAAS;AAAA;AAAA,MAGnC,YAAY;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,MACP,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB,MAAM;AAAA,MACN;AAAA;AAAA,MAGA;AAAA;AAAA,QAEE,WAAW,UAAS,iDAAgB,UAAS,SACzC,EAAE,MAAM,cAAc,IACtB;AAAA;AAAA;AAAA,MAGN,kBAAkB,2CAAa;AAAA;AAAA,MAG/B,UAAU,0BAA0B,MAAM;AAAA,IAC5C;AAEA,YAAQ,MAAM;AAAA,MACZ,KAAK,WAAW;AACd,cAAM,EAAE,OAAO,aAAa,aAAa,IAAI,aAAa,EAAE,KAAK,CAAC;AAClE,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,GAAG;AAAA,YACH;AAAA,YACA;AAAA,UACF;AAAA,UACA,UAAU,CAAC,GAAG,UAAU,GAAG,YAAY;AAAA,QACzC;AAAA,MACF;AAAA,MAEA,KAAK,eAAe;AAClB,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,GAAG;AAAA,YACH;AAAA;AAAA,cAEE,WAAW,QAAQ,EAAE,MAAM,cAAc,IAAI;AAAA;AAAA,UACjD;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,MAEA,KAAK,eAAe;AAClB,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,GAAG;AAAA,YACH,aAAa;AAAA,cACX,MAAM;AAAA,cACN,UAAU,EAAE,MAAM,KAAK,KAAK,KAAK;AAAA,YACnC;AAAA,YACA,OAAO;AAAA,cACL;AAAA,gBACE,MAAM;AAAA,gBACN,UAAU;AAAA,kBACR,MAAM,KAAK,KAAK;AAAA,kBAChB,aAAa,KAAK,KAAK;AAAA,kBACvB,YAAY,KAAK,KAAK;AAAA,gBACxB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,WACJ,SAC6D;AAxMjE;AAyMI,UAAM,EAAE,MAAM,SAAS,IAAI,KAAK,QAAQ,EAAE,GAAG,SAAS,QAAQ,MAAM,CAAC;AAErE,UAAM,OAAO,KAAK,UAAU,IAAI;AAEhC,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP,UAAU;AAAA,IACZ,IAAI,UAAM,sCAAc;AAAA,MACtB,KAAK,KAAK,OAAO,IAAI;AAAA,QACnB,MAAM;AAAA,QACN,SAAS,KAAK;AAAA,MAChB,CAAC;AAAA,MACD,aAAS,uCAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;AAAA,MAC9D,MAAM;AAAA,MACN,uBAAuB;AAAA,MACvB,+BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA,aAAa,QAAQ;AAAA,MACrB,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAChD,UAAM,SAAS,SAAS,QAAQ,CAAC;AAEjC,WAAO;AAAA,MACL,OAAM,YAAO,QAAQ,YAAf,YAA0B;AAAA,MAChC,YAAW,YAAO,QAAQ,cAAf,YAA4B;AAAA,MACvC,YAAW,YAAO,QAAQ,eAAf,mBAA2B,IAAI,cAAS;AAtOzD,YAAAC;AAsO6D;AAAA,UACrD,cAAc;AAAA,UACd,aAAYA,MAAA,SAAS,OAAT,OAAAA,UAAe,mCAAW;AAAA,UACtC,UAAU,SAAS,SAAS;AAAA,UAC5B,MAAM,SAAS,SAAS;AAAA,QAC1B;AAAA;AAAA,MACA,cAAc,oBAAoB,OAAO,aAAa;AAAA,MACtD,OAAO;AAAA,QACL,eAAc,oBAAS,UAAT,mBAAgB,kBAAhB,YAAiC;AAAA,QAC/C,mBAAkB,oBAAS,UAAT,mBAAgB,sBAAhB,YAAqC;AAAA,MACzD;AAAA,MACA,SAAS,EAAE,WAAW,YAAY;AAAA,MAClC,aAAa,EAAE,SAAS,iBAAiB,MAAM,YAAY;AAAA,MAC3D,UAAU,oBAAoB,QAAQ;AAAA,MACtC;AAAA,MACA,SAAS,EAAE,KAAK;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,MAAM,SACJ,SAC2D;AAC3D,UAAM,EAAE,MAAM,SAAS,IAAI,KAAK,QAAQ,EAAE,GAAG,SAAS,QAAQ,KAAK,CAAC;AAEpE,UAAM,OAAO,KAAK,UAAU,EAAE,GAAG,MAAM,QAAQ,KAAK,CAAC;AAErD,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,UAAM,sCAAc;AAAA,MAC/D,KAAK,KAAK,OAAO,IAAI;AAAA,QACnB,MAAM;AAAA,QACN,SAAS,KAAK;AAAA,MAChB,CAAC;AAAA,MACD,aAAS,uCAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;AAAA,MAC9D,MAAM;AAAA,QACJ,GAAG;AAAA,QACH,QAAQ;AAAA,MACV;AAAA,MACA,uBAAuB;AAAA,MACvB,+BACE,yDAAiC,mBAAmB;AAAA,MACtD,aAAa,QAAQ;AAAA,MACrB,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAEhD,UAAM,YAQD,CAAC;AAEN,QAAI,eAA4C;AAChD,QAAI,QAGA;AAAA,MACF,cAAc;AAAA,MACd,kBAAkB;AAAA,IACpB;AACA,QAAI,eAAe;AAEnB,QAAI;AACJ,WAAO;AAAA,MACL,QAAQ,SAAS;AAAA,QACf,IAAI,gBAGF;AAAA,UACA,UAAU,OAAO,YAAY;AA9SvC;AAgTY,gBAAI,CAAC,MAAM,SAAS;AAClB,6BAAe;AACf,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;AAAA,YACF;AAEA,kBAAM,QAAQ,MAAM;AAGpB,gBAAI,WAAW,OAAO;AACpB,6BAAe;AACf,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;AAAA,YACF;AAEA,gBAAI,cAAc;AAChB,6BAAe;AAEf,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,GAAG,oBAAoB,KAAK;AAAA,cAC9B,CAAC;AAAA,YACH;AAEA,kBAAI,WAAM,WAAN,mBAAc,UAAS,MAAM;AAC/B,sBAAQ;AAAA,gBACN,eAAc,WAAM,OAAO,MAAM,kBAAnB,YAAoC;AAAA,gBAClD,mBACE,WAAM,OAAO,MAAM,sBAAnB,YAAwC;AAAA,cAC5C;AAAA,YACF;AAEA,kBAAM,SAAS,MAAM,QAAQ,CAAC;AAE9B,iBAAI,iCAAQ,kBAAiB,MAAM;AACjC,6BAAe,oBAAoB,OAAO,aAAa;AAAA,YACzD;AAEA,iBAAI,iCAAQ,UAAS,MAAM;AACzB;AAAA,YACF;AAEA,kBAAM,QAAQ,OAAO;AAErB,gBAAI,MAAM,aAAa,QAAQ,MAAM,UAAU,SAAS,GAAG;AACzD,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,WAAW,MAAM;AAAA,cACnB,CAAC;AAAA,YACH;AAEA,gBAAI,MAAM,WAAW,QAAQ,MAAM,QAAQ,SAAS,GAAG;AACrD,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,WAAW,MAAM;AAAA,cACnB,CAAC;AAAA,YACH;AAEA,gBAAI,MAAM,cAAc,MAAM;AAC5B,yBAAW,iBAAiB,MAAM,YAAY;AAC5C,sBAAM,QAAQ,cAAc;AAE5B,oBAAI,UAAU,KAAK,KAAK,MAAM;AAC5B,sBAAI,cAAc,SAAS,YAAY;AACrC,0BAAM,IAAI,0CAAyB;AAAA,sBACjC,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX,CAAC;AAAA,kBACH;AAEA,sBAAI,cAAc,MAAM,MAAM;AAC5B,0BAAM,IAAI,0CAAyB;AAAA,sBACjC,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX,CAAC;AAAA,kBACH;AAEA,wBAAI,mBAAc,aAAd,mBAAwB,SAAQ,MAAM;AACxC,0BAAM,IAAI,0CAAyB;AAAA,sBACjC,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX,CAAC;AAAA,kBACH;AAEA,4BAAU,KAAK,IAAI;AAAA,oBACjB,IAAI,cAAc;AAAA,oBAClB,MAAM;AAAA,oBACN,UAAU;AAAA,sBACR,MAAM,cAAc,SAAS;AAAA,sBAC7B,YAAW,mBAAc,SAAS,cAAvB,YAAoC;AAAA,oBACjD;AAAA,oBACA,aAAa;AAAA,kBACf;AAEA,wBAAMC,YAAW,UAAU,KAAK;AAEhC,wBACE,KAAAA,UAAS,aAAT,mBAAmB,SAAQ,UAC3B,KAAAA,UAAS,aAAT,mBAAmB,cAAa,MAChC;AAEA,wBAAIA,UAAS,SAAS,UAAU,SAAS,GAAG;AAC1C,iCAAW,QAAQ;AAAA,wBACjB,MAAM;AAAA,wBACN,cAAc;AAAA,wBACd,YAAYA,UAAS;AAAA,wBACrB,UAAUA,UAAS,SAAS;AAAA,wBAC5B,eAAeA,UAAS,SAAS;AAAA,sBACnC,CAAC;AAAA,oBACH;AAIA,4BAAI,uCAAeA,UAAS,SAAS,SAAS,GAAG;AAC/C,iCAAW,QAAQ;AAAA,wBACjB,MAAM;AAAA,wBACN,cAAc;AAAA,wBACd,aAAY,KAAAA,UAAS,OAAT,gBAAe,mCAAW;AAAA,wBACtC,UAAUA,UAAS,SAAS;AAAA,wBAC5B,MAAMA,UAAS,SAAS;AAAA,sBAC1B,CAAC;AACD,sBAAAA,UAAS,cAAc;AAAA,oBACzB;AAAA,kBACF;AAEA;AAAA,gBACF;AAGA,sBAAM,WAAW,UAAU,KAAK;AAEhC,oBAAI,SAAS,aAAa;AACxB;AAAA,gBACF;AAEA,sBAAI,mBAAc,aAAd,mBAAwB,cAAa,MAAM;AAC7C,2BAAS,SAAU,cACjB,yBAAc,aAAd,mBAAwB,cAAxB,YAAqC;AAAA,gBACzC;AAGA,2BAAW,QAAQ;AAAA,kBACjB,MAAM;AAAA,kBACN,cAAc;AAAA,kBACd,YAAY,SAAS;AAAA,kBACrB,UAAU,SAAS,SAAS;AAAA,kBAC5B,gBAAe,mBAAc,SAAS,cAAvB,YAAoC;AAAA,gBACrD,CAAC;AAGD,sBACE,cAAS,aAAT,mBAAmB,SAAQ,UAC3B,cAAS,aAAT,mBAAmB,cAAa,YAChC,uCAAe,SAAS,SAAS,SAAS,GAC1C;AACA,6BAAW,QAAQ;AAAA,oBACjB,MAAM;AAAA,oBACN,cAAc;AAAA,oBACd,aAAY,cAAS,OAAT,gBAAe,mCAAW;AAAA,oBACtC,UAAU,SAAS,SAAS;AAAA,oBAC5B,MAAM,SAAS,SAAS;AAAA,kBAC1B,CAAC;AACD,2BAAS,cAAc;AAAA,gBACzB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UAEA,MAAM,YAAY;AAxd5B;AAydY,uBAAW,QAAQ;AAAA,cACjB,MAAM;AAAA,cACN;AAAA,cACA,OAAO;AAAA,gBACL,eAAc,WAAM,iBAAN,YAAsB;AAAA,gBACpC,mBAAkB,WAAM,qBAAN,YAA0B;AAAA,cAC9C;AAAA,cACA,GAAI,oBAAoB,OAAO,EAAE,iBAAiB,IAAI,CAAC;AAAA,YACzD,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,SAAS,EAAE,WAAW,YAAY;AAAA,MAClC,aAAa,EAAE,SAAS,gBAAgB;AAAA,MACxC;AAAA,MACA,SAAS,EAAE,KAAK;AAAA,IAClB;AAAA,EACF;AACF;AAIA,IAAM,yBAAyB,cAAE,OAAO;AAAA,EACtC,IAAI,cAAE,OAAO,EAAE,QAAQ;AAAA,EACvB,SAAS,cAAE,OAAO,EAAE,QAAQ;AAAA,EAC5B,OAAO,cAAE,OAAO,EAAE,QAAQ;AAAA,EAC1B,SAAS,cAAE;AAAA,IACT,cAAE,OAAO;AAAA,MACP,SAAS,cAAE,OAAO;AAAA,QAChB,SAAS,cAAE,OAAO,EAAE,QAAQ;AAAA,QAC5B,WAAW,cAAE,OAAO,EAAE,QAAQ;AAAA,QAC9B,YAAY,cACT;AAAA,UACC,cAAE,OAAO;AAAA,YACP,IAAI,cAAE,OAAO,EAAE,QAAQ;AAAA,YACvB,MAAM,cAAE,QAAQ,UAAU;AAAA,YAC1B,UAAU,cAAE,OAAO;AAAA,cACjB,MAAM,cAAE,OAAO;AAAA,cACf,WAAW,cAAE,OAAO;AAAA,YACtB,CAAC;AAAA,UACH,CAAC;AAAA,QACH,EACC,QAAQ;AAAA,MACb,CAAC;AAAA,MACD,OAAO,cAAE,OAAO;AAAA,MAChB,eAAe,cAAE,OAAO,EAAE,QAAQ;AAAA,IACpC,CAAC;AAAA,EACH;AAAA,EACA,OAAO,cACJ,OAAO;AAAA,IACN,eAAe,cAAE,OAAO,EAAE,QAAQ;AAAA,IAClC,mBAAmB,cAAE,OAAO,EAAE,QAAQ;AAAA,EACxC,CAAC,EACA,QAAQ;AACb,CAAC;AAID,IAAM,sBAAsB,cAAE,MAAM;AAAA,EAClC,cAAE,OAAO;AAAA,IACP,IAAI,cAAE,OAAO,EAAE,QAAQ;AAAA,IACvB,SAAS,cAAE,OAAO,EAAE,QAAQ;AAAA,IAC5B,OAAO,cAAE,OAAO,EAAE,QAAQ;AAAA,IAC1B,SAAS,cAAE;AAAA,MACT,cAAE,OAAO;AAAA,QACP,OAAO,cACJ,OAAO;AAAA,UACN,SAAS,cAAE,OAAO,EAAE,QAAQ;AAAA,UAC5B,WAAW,cAAE,OAAO,EAAE,QAAQ;AAAA,UAC9B,YAAY,cACT;AAAA,YACC,cAAE,OAAO;AAAA,cACP,OAAO,cAAE,OAAO;AAAA,cAChB,IAAI,cAAE,OAAO,EAAE,QAAQ;AAAA,cACvB,MAAM,cAAE,QAAQ,UAAU,EAAE,SAAS;AAAA,cACrC,UAAU,cAAE,OAAO;AAAA,gBACjB,MAAM,cAAE,OAAO,EAAE,QAAQ;AAAA,gBACzB,WAAW,cAAE,OAAO,EAAE,QAAQ;AAAA,cAChC,CAAC;AAAA,YACH,CAAC;AAAA,UACH,EACC,QAAQ;AAAA,QACb,CAAC,EACA,QAAQ;AAAA,QACX,eAAe,cAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,QAC9C,OAAO,cAAE,OAAO;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,IACA,QAAQ,cACL,OAAO;AAAA,MACN,OAAO,cACJ,OAAO;AAAA,QACN,eAAe,cAAE,OAAO,EAAE,QAAQ;AAAA,QAClC,mBAAmB,cAAE,OAAO,EAAE,QAAQ;AAAA,MACxC,CAAC,EACA,QAAQ;AAAA,IACb,CAAC,EACA,QAAQ;AAAA,EACb,CAAC;AAAA,EACD;AACF,CAAC;;;AMzjBD,IAAAC,yBAMO;AACP,IAAAC,cAAkB;AAOlB,IAAM,4BAA4B,cAAE,OAAO;AAAA,EACzC,UAAU,cAAE,OAAO,EAAE,QAAQ;AAAA,EAC7B,QAAQ,cAAE,OAAO,EAAE,QAAQ;AAAA,EAC3B,gBAAgB,cAAE,OAAO,EAAE,QAAQ;AAAA,EACnC,aAAa,cAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ;AAAA,EAC9C,wBAAwB,cAAE,MAAM,cAAE,OAAO,CAAC,EAAE,QAAQ;AACtD,CAAC;AAYM,IAAM,yBAAN,MAA6D;AAAA,EAOlE,YACW,SACQ,QACjB;AAFS;AACQ;AARnB,SAAS,uBAAuB;AAAA,EAS7B;AAAA,EAPH,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EAOQ,QAAQ;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAsD;AApDxD;AAqDI,UAAM,WAA8C,CAAC;AAGrD,UAAM,kBAAc,6CAAqB;AAAA,MACvC,UAAU;AAAA,MACV;AAAA,MACA,QAAQ;AAAA,IACV,CAAC;AAGD,UAAM,WAAW,IAAI,SAAS;AAC9B,UAAM,OACJ,iBAAiB,aACb,IAAI,KAAK,CAAC,KAAK,CAAC,IAChB,IAAI,KAAK,KAAC,kDAA0B,KAAK,CAAC,CAAC;AAEjD,aAAS,OAAO,SAAS,KAAK,OAAO;AACrC,aAAS,OAAO,QAAQ,IAAI,KAAK,CAAC,IAAI,GAAG,SAAS,EAAE,MAAM,UAAU,CAAC,CAAC;AAGtE,QAAI,aAAa;AACf,YAAM,4BAGF;AAAA,QACF,WAAU,iBAAY,aAAZ,YAAwB;AAAA,QAClC,SAAQ,iBAAY,WAAZ,YAAsB;AAAA,QAC9B,kBAAiB,iBAAY,mBAAZ,YAA8B;AAAA,QAC/C,cAAa,iBAAY,gBAAZ,YAA2B;AAAA,QACxC,0BACE,iBAAY,2BAAZ,YAAsC;AAAA,MAC1C;AAEA,iBAAW,OAAO,2BAA2B;AAC3C,cAAM,QACJ,0BACE,GACF;AACF,YAAI,UAAU,QAAW;AACvB,mBAAS,OAAO,KAAK,OAAO,KAAK,CAAC;AAAA,QACpC;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,WACJ,SACkE;AAzGtE;AA0GI,UAAM,eAAc,sBAAK,OAAO,cAAZ,mBAAuB,gBAAvB,4CAA0C,oBAAI,KAAK;AACvE,UAAM,EAAE,UAAU,SAAS,IAAI,KAAK,QAAQ,OAAO;AAEnD,UAAM;AAAA,MACJ,OAAO;AAAA,MACP;AAAA,MACA,UAAU;AAAA,IACZ,IAAI,UAAM,0CAAkB;AAAA,MAC1B,KAAK,KAAK,OAAO,IAAI;AAAA,QACnB,MAAM;AAAA,QACN,SAAS,KAAK;AAAA,MAChB,CAAC;AAAA,MACD,aAAS,uCAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;AAAA,MAC9D;AAAA,MACA,uBAAuB;AAAA,MACvB,+BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA,aAAa,QAAQ;AAAA,MACrB,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,WAAO;AAAA,MACL,MAAM,SAAS;AAAA,MACf,WACE,oBAAS,aAAT,mBAAmB,IAAI,cAAY;AAAA,QACjC,MAAM,QAAQ;AAAA,QACd,aAAa,QAAQ;AAAA,QACrB,WAAW,QAAQ;AAAA,MACrB,QAJA,YAIO,CAAC;AAAA,MACV,UAAU,SAAS;AAAA,MACnB,mBAAmB,SAAS;AAAA,MAC5B;AAAA,MACA,UAAU;AAAA,QACR,WAAW;AAAA,QACX,SAAS,KAAK;AAAA,QACd,SAAS;AAAA,QACT,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,kCAAkC,cAAE,OAAO;AAAA,EAC/C,MAAM,cAAE,OAAO;AAAA,EACf,UAAU,cAAE,OAAO;AAAA,EACnB,UAAU,cAAE,OAAO;AAAA,EACnB,MAAM,cAAE,OAAO;AAAA,EACf,UAAU,cAAE;AAAA,IACV,cAAE,OAAO;AAAA,MACP,IAAI,cAAE,OAAO;AAAA,MACb,MAAM,cAAE,OAAO;AAAA,MACf,OAAO,cAAE,OAAO;AAAA,MAChB,KAAK,cAAE,OAAO;AAAA,MACd,MAAM,cAAE,OAAO;AAAA,MACf,QAAQ,cAAE,MAAM,cAAE,OAAO,CAAC;AAAA,MAC1B,aAAa,cAAE,OAAO;AAAA,MACtB,aAAa,cAAE,OAAO;AAAA,MACtB,mBAAmB,cAAE,OAAO;AAAA,MAC5B,gBAAgB,cAAE,OAAO;AAAA,IAC3B,CAAC;AAAA,EACH;AAAA,EACA,QAAQ,cAAE,OAAO;AAAA,IACf,IAAI,cAAE,OAAO;AAAA,EACf,CAAC;AACH,CAAC;;;AP7GM,SAAS,WAAW,UAAgC,CAAC,GAAiB;AA9D7E;AA+DE,QAAM,WACJ,sDAAqB,QAAQ,OAAO,MAApC,YAAyC;AAE3C,QAAM,aAAa,OAAO;AAAA,IACxB,eAAe,cAAU,mCAAW;AAAA,MAClC,QAAQ,QAAQ;AAAA,MAChB,yBAAyB;AAAA,MACzB,aAAa;AAAA,IACf,CAAC,CAAC;AAAA,IACF,GAAG,QAAQ;AAAA,EACb;AAEA,QAAM,kBAAkB,CACtB,SACA,WAA6B,CAAC,MAE9B,IAAI,sBAAsB,SAAS,UAAU;AAAA,IAC3C,UAAU;AAAA,IACV,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,OAAO,GAAG,IAAI;AAAA,IACpC,SAAS;AAAA,IACT,OAAO,QAAQ;AAAA,EACjB,CAAC;AAEH,QAAM,sBAAsB,CAC1B,SACA,aACG;AACH,QAAI,YAAY;AACd,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C;AAEA,QAAM,2BAA2B,CAAC,YAAsC;AACtE,WAAO,IAAI,uBAAuB,SAAS;AAAA,MACzC,UAAU;AAAA,MACV,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,OAAO,GAAG,IAAI;AAAA,MACpC,SAAS;AAAA,MACT,OAAO,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH;AAEA,QAAM,WAAW,SACf,SACA,UACA;AACA,WAAO,oBAAoB,SAAS,QAAQ;AAAA,EAC9C;AAEA,WAAS,gBAAgB;AACzB,WAAS,OAAO;AAChB,WAAS,qBAAqB,CAAC,YAAoB;AACjD,UAAM,IAAI,kCAAiB,EAAE,SAAS,WAAW,qBAAqB,CAAC;AAAA,EACzE;AACA,WAAS,gBAAgB;AAEzB,SAAO;AACT;AAKO,IAAM,OAAO,WAAW;", "names": ["import_provider", "import_provider_utils", "import_provider", "import_provider_utils", "import_zod", "import_provider_utils", "import_provider", "_a", "toolCall", "import_provider_utils", "import_zod"]}
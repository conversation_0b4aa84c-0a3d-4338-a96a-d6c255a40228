{"version": 3, "sources": ["../src/index.ts", "../src/cerebras-provider.ts"], "sourcesContent": ["export { createCerebras, cerebras } from './cerebras-provider';\nexport type {\n  CerebrasProvider,\n  CerebrasProviderSettings,\n} from './cerebras-provider';\nexport type { CerebrasErrorData } from './cerebras-provider';\n", "import { OpenAICompatibleChatLanguageModel } from '@ai-sdk/openai-compatible';\nimport {\n  LanguageModelV1,\n  NoSuchModelError,\n  ProviderV1,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  loadApiKey,\n  withoutTrailingSlash,\n} from '@ai-sdk/provider-utils';\nimport {\n  CerebrasChatModelId,\n  CerebrasChatSettings,\n} from './cerebras-chat-settings';\nimport { z } from 'zod';\nimport { ProviderErrorStructure } from '@ai-sdk/openai-compatible';\n\n// Add error schema and structure\nconst cerebrasErrorSchema = z.object({\n  message: z.string(),\n  type: z.string(),\n  param: z.string(),\n  code: z.string(),\n});\n\nexport type CerebrasErrorData = z.infer<typeof cerebrasErrorSchema>;\n\nconst cerebrasErrorStructure: ProviderErrorStructure<CerebrasErrorData> = {\n  errorSchema: cerebrasErrorSchema,\n  errorToMessage: data => data.message,\n};\n\nexport interface CerebrasProviderSettings {\n  /**\nCerebras API key.\n*/\n  apiKey?: string;\n  /**\nBase URL for the API calls.\n*/\n  baseURL?: string;\n  /**\nCustom headers to include in the requests.\n*/\n  headers?: Record<string, string>;\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n*/\n  fetch?: FetchFunction;\n}\n\nexport interface CerebrasProvider extends ProviderV1 {\n  /**\nCreates a Cerebras model for text generation.\n*/\n  (\n    modelId: CerebrasChatModelId,\n    settings?: CerebrasChatSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates a Cerebras model for text generation.\n*/\n  languageModel(\n    modelId: CerebrasChatModelId,\n    settings?: CerebrasChatSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates a Cerebras chat model for text generation.\n*/\n  chat(\n    modelId: CerebrasChatModelId,\n    settings?: CerebrasChatSettings,\n  ): LanguageModelV1;\n}\n\nexport function createCerebras(\n  options: CerebrasProviderSettings = {},\n): CerebrasProvider {\n  const baseURL = withoutTrailingSlash(\n    options.baseURL ?? 'https://api.cerebras.ai/v1',\n  );\n  const getHeaders = () => ({\n    Authorization: `Bearer ${loadApiKey({\n      apiKey: options.apiKey,\n      environmentVariableName: 'CEREBRAS_API_KEY',\n      description: 'Cerebras API key',\n    })}`,\n    ...options.headers,\n  });\n\n  const createLanguageModel = (\n    modelId: CerebrasChatModelId,\n    settings: CerebrasChatSettings = {},\n  ) => {\n    return new OpenAICompatibleChatLanguageModel(modelId, settings, {\n      provider: `cerebras.chat`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch,\n      defaultObjectGenerationMode: 'tool',\n      errorStructure: cerebrasErrorStructure,\n    });\n  };\n\n  const provider = (\n    modelId: CerebrasChatModelId,\n    settings?: CerebrasChatSettings,\n  ) => createLanguageModel(modelId, settings);\n\n  provider.languageModel = createLanguageModel;\n  provider.chat = createLanguageModel;\n  provider.textEmbeddingModel = (modelId: string) => {\n    throw new NoSuchModelError({ modelId, modelType: 'textEmbeddingModel' });\n  };\n\n  return provider;\n}\n\nexport const cerebras = createCerebras();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,+BAAkD;AAClD,sBAIO;AACP,4BAIO;AAKP,iBAAkB;AAIlB,IAAM,sBAAsB,aAAE,OAAO;AAAA,EACnC,SAAS,aAAE,OAAO;AAAA,EAClB,MAAM,aAAE,OAAO;AAAA,EACf,OAAO,aAAE,OAAO;AAAA,EAChB,MAAM,aAAE,OAAO;AACjB,CAAC;AAID,IAAM,yBAAoE;AAAA,EACxE,aAAa;AAAA,EACb,gBAAgB,UAAQ,KAAK;AAC/B;AAgDO,SAAS,eACd,UAAoC,CAAC,GACnB;AAjFpB;AAkFE,QAAM,cAAU;AAAA,KACd,aAAQ,YAAR,YAAmB;AAAA,EACrB;AACA,QAAM,aAAa,OAAO;AAAA,IACxB,eAAe,cAAU,kCAAW;AAAA,MAClC,QAAQ,QAAQ;AAAA,MAChB,yBAAyB;AAAA,MACzB,aAAa;AAAA,IACf,CAAC,CAAC;AAAA,IACF,GAAG,QAAQ;AAAA,EACb;AAEA,QAAM,sBAAsB,CAC1B,SACA,WAAiC,CAAC,MAC/B;AACH,WAAO,IAAI,2DAAkC,SAAS,UAAU;AAAA,MAC9D,UAAU;AAAA,MACV,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,OAAO,GAAG,IAAI;AAAA,MACpC,SAAS;AAAA,MACT,OAAO,QAAQ;AAAA,MACf,6BAA6B;AAAA,MAC7B,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAEA,QAAM,WAAW,CACf,SACA,aACG,oBAAoB,SAAS,QAAQ;AAE1C,WAAS,gBAAgB;AACzB,WAAS,OAAO;AAChB,WAAS,qBAAqB,CAAC,YAAoB;AACjD,UAAM,IAAI,iCAAiB,EAAE,SAAS,WAAW,qBAAqB,CAAC;AAAA,EACzE;AAEA,SAAO;AACT;AAEO,IAAM,WAAW,eAAe;", "names": []}
// src/cerebras-provider.ts
import { OpenAICompatibleChatLanguageModel } from "@ai-sdk/openai-compatible";
import {
  NoSuchModelError
} from "@ai-sdk/provider";
import {
  loadApiKey,
  withoutTrailingSlash
} from "@ai-sdk/provider-utils";
import { z } from "zod";
var cerebrasErrorSchema = z.object({
  message: z.string(),
  type: z.string(),
  param: z.string(),
  code: z.string()
});
var cerebrasErrorStructure = {
  errorSchema: cerebrasErrorSchema,
  errorToMessage: (data) => data.message
};
function createCerebras(options = {}) {
  var _a;
  const baseURL = withoutTrailingSlash(
    (_a = options.baseURL) != null ? _a : "https://api.cerebras.ai/v1"
  );
  const getHeaders = () => ({
    Authorization: `Bearer ${loadApiKey({
      apiKey: options.apiKey,
      environmentVariableName: "CEREBRAS_API_KEY",
      description: "Cerebras API key"
    })}`,
    ...options.headers
  });
  const createLanguageModel = (modelId, settings = {}) => {
    return new OpenAICompatibleChatLanguageModel(modelId, settings, {
      provider: `cerebras.chat`,
      url: ({ path }) => `${baseURL}${path}`,
      headers: getHeaders,
      fetch: options.fetch,
      defaultObjectGenerationMode: "tool",
      errorStructure: cerebrasErrorStructure
    });
  };
  const provider = (modelId, settings) => createLanguageModel(modelId, settings);
  provider.languageModel = createLanguageModel;
  provider.chat = createLanguageModel;
  provider.textEmbeddingModel = (modelId) => {
    throw new NoSuchModelError({ modelId, modelType: "textEmbeddingModel" });
  };
  return provider;
}
var cerebras = createCerebras();
export {
  cerebras,
  createCerebras
};
//# sourceMappingURL=index.mjs.map
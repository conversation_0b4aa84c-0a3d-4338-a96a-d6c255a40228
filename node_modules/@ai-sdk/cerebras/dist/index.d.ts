import { ProviderV1, LanguageModelV1 } from '@ai-sdk/provider';
import { FetchFunction } from '@ai-sdk/provider-utils';
import { OpenAICompatibleChatSettings } from '@ai-sdk/openai-compatible';
import { z } from 'zod';

type CerebrasChatModelId = 'llama3.1-8b' | 'llama3.1-70b' | 'llama-3.3-70b' | (string & {});
interface CerebrasChatSettings extends OpenAICompatibleChatSettings {
}

declare const cerebrasErrorSchema: z.ZodObject<{
    message: z.ZodString;
    type: z.ZodString;
    param: z.ZodString;
    code: z.ZodString;
}, "strip", z.ZodTypeAny, {
    message: string;
    type: string;
    param: string;
    code: string;
}, {
    message: string;
    type: string;
    param: string;
    code: string;
}>;
type CerebrasErrorData = z.infer<typeof cerebrasErrorSchema>;
interface CerebrasProviderSettings {
    /**
  Cerebras API key.
  */
    apiKey?: string;
    /**
  Base URL for the API calls.
  */
    baseURL?: string;
    /**
  Custom headers to include in the requests.
  */
    headers?: Record<string, string>;
    /**
  Custom fetch implementation. You can use it as a middleware to intercept requests,
  or to provide a custom fetch implementation for e.g. testing.
  */
    fetch?: FetchFunction;
}
interface CerebrasProvider extends ProviderV1 {
    /**
  Creates a Cerebras model for text generation.
  */
    (modelId: CerebrasChatModelId, settings?: CerebrasChatSettings): LanguageModelV1;
    /**
  Creates a Cerebras model for text generation.
  */
    languageModel(modelId: CerebrasChatModelId, settings?: CerebrasChatSettings): LanguageModelV1;
    /**
  Creates a Cerebras chat model for text generation.
  */
    chat(modelId: CerebrasChatModelId, settings?: CerebrasChatSettings): LanguageModelV1;
}
declare function createCerebras(options?: CerebrasProviderSettings): CerebrasProvider;
declare const cerebras: CerebrasProvider;

export { type CerebrasErrorData, type CerebrasProvider, type CerebrasProviderSettings, cerebras, createCerebras };

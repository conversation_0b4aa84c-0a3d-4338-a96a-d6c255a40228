{"version": 3, "sources": ["../../src/test/mock-fetch.ts"], "sourcesContent": ["import { fail } from 'node:assert';\nimport { vi } from 'vitest';\n\nexport function mockFetchTextStream({\n  url,\n  chunks,\n}: {\n  url: string;\n  chunks: string[];\n}) {\n  vi.spyOn(global, 'fetch').mockImplementation(async () => {\n    function* generateChunks() {\n      for (const chunk of chunks) {\n        yield new TextEncoder().encode(chunk);\n      }\n    }\n\n    const chunkGenerator = generateChunks();\n\n    return {\n      url,\n      ok: true,\n      status: 200,\n      bodyUsed: false,\n      headers: new Map() as any as Headers,\n      body: {\n        getReader() {\n          return {\n            read() {\n              return Promise.resolve(chunkGenerator.next());\n            },\n            releaseLock() {},\n            cancel() {},\n          };\n        },\n      },\n    } as unknown as Response;\n  });\n}\n\nexport function mockFetchDataStream({\n  url,\n  chunks,\n  maxCalls,\n}: {\n  url: string;\n  chunks: string[];\n  maxCalls?: number;\n}) {\n  async function* generateChunks() {\n    const encoder = new TextEncoder();\n    for (const chunk of chunks) {\n      yield encoder.encode(chunk);\n    }\n  }\n\n  return mockFetchDataStreamWithGenerator({\n    url,\n    chunkGenerator: generateChunks(),\n    maxCalls,\n  });\n}\n\nexport function mockFetchDataStreamWithGenerator({\n  url,\n  chunkGenerator,\n  maxCalls,\n}: {\n  url: string;\n  chunkGenerator: AsyncGenerator<Uint8Array, void, unknown>;\n  maxCalls?: number;\n}) {\n  let requestBodyResolve: ((value?: unknown) => void) | undefined;\n  const requestBodyPromise = new Promise(resolve => {\n    requestBodyResolve = resolve;\n  });\n\n  let callCount = 0;\n\n  vi.spyOn(global, 'fetch').mockImplementation(async (url, init) => {\n    if (maxCalls !== undefined && ++callCount >= maxCalls) {\n      throw new Error('Too many calls');\n    }\n\n    requestBodyResolve?.(init!.body as string);\n\n    return {\n      url,\n      ok: true,\n      status: 200,\n      bodyUsed: false,\n      body: new ReadableStream({\n        async start(controller) {\n          for await (const chunk of chunkGenerator) {\n            controller.enqueue(chunk);\n          }\n          controller.close();\n        },\n      }),\n    } as Response;\n  });\n\n  return {\n    requestBody: requestBodyPromise,\n  };\n}\n\nexport function mockFetchError({\n  statusCode,\n  errorMessage,\n}: {\n  statusCode: number;\n  errorMessage: string;\n}) {\n  vi.spyOn(global, 'fetch').mockImplementation(async () => {\n    return {\n      url: 'https://example.com/api/chat',\n      ok: false,\n      status: statusCode,\n      bodyUsed: false,\n      body: {\n        getReader() {\n          return {\n            read() {\n              return Promise.resolve(errorMessage);\n            },\n            releaseLock() {},\n            cancel() {},\n          };\n        },\n      },\n      text: () => Promise.resolve(errorMessage),\n    } as unknown as Response;\n  });\n}\n"], "mappings": ";AACA,SAAS,UAAU;AAEZ,SAAS,oBAAoB;AAAA,EAClC;AAAA,EACA;AACF,GAGG;AACD,KAAG,MAAM,QAAQ,OAAO,EAAE,mBAAmB,YAAY;AACvD,cAAU,iBAAiB;AACzB,iBAAW,SAAS,QAAQ;AAC1B,cAAM,IAAI,YAAY,EAAE,OAAO,KAAK;AAAA,MACtC;AAAA,IACF;AAEA,UAAM,iBAAiB,eAAe;AAEtC,WAAO;AAAA,MACL;AAAA,MACA,IAAI;AAAA,MACJ,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,SAAS,oBAAI,IAAI;AAAA,MACjB,MAAM;AAAA,QACJ,YAAY;AACV,iBAAO;AAAA,YACL,OAAO;AACL,qBAAO,QAAQ,QAAQ,eAAe,KAAK,CAAC;AAAA,YAC9C;AAAA,YACA,cAAc;AAAA,YAAC;AAAA,YACf,SAAS;AAAA,YAAC;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEO,SAAS,oBAAoB;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AACF,GAIG;AACD,kBAAgB,iBAAiB;AAC/B,UAAM,UAAU,IAAI,YAAY;AAChC,eAAW,SAAS,QAAQ;AAC1B,YAAM,QAAQ,OAAO,KAAK;AAAA,IAC5B;AAAA,EACF;AAEA,SAAO,iCAAiC;AAAA,IACtC;AAAA,IACA,gBAAgB,eAAe;AAAA,IAC/B;AAAA,EACF,CAAC;AACH;AAEO,SAAS,iCAAiC;AAAA,EAC/C;AAAA,EACA;AAAA,EACA;AACF,GAIG;AACD,MAAI;AACJ,QAAM,qBAAqB,IAAI,QAAQ,aAAW;AAChD,yBAAqB;AAAA,EACvB,CAAC;AAED,MAAI,YAAY;AAEhB,KAAG,MAAM,QAAQ,OAAO,EAAE,mBAAmB,OAAOA,MAAK,SAAS;AAChE,QAAI,aAAa,UAAa,EAAE,aAAa,UAAU;AACrD,YAAM,IAAI,MAAM,gBAAgB;AAAA,IAClC;AAEA,6DAAqB,KAAM;AAE3B,WAAO;AAAA,MACL,KAAAA;AAAA,MACA,IAAI;AAAA,MACJ,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,MAAM,IAAI,eAAe;AAAA,QACvB,MAAM,MAAM,YAAY;AACtB,2BAAiB,SAAS,gBAAgB;AACxC,uBAAW,QAAQ,KAAK;AAAA,UAC1B;AACA,qBAAW,MAAM;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AAED,SAAO;AAAA,IACL,aAAa;AAAA,EACf;AACF;AAEO,SAAS,eAAe;AAAA,EAC7B;AAAA,EACA;AACF,GAGG;AACD,KAAG,MAAM,QAAQ,OAAO,EAAE,mBAAmB,YAAY;AACvD,WAAO;AAAA,MACL,KAAK;AAAA,MACL,IAAI;AAAA,MACJ,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,YAAY;AACV,iBAAO;AAAA,YACL,OAAO;AACL,qBAAO,QAAQ,QAAQ,YAAY;AAAA,YACrC;AAAA,YACA,cAAc;AAAA,YAAC;AAAA,YACf,SAAS;AAAA,YAAC;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM,MAAM,QAAQ,QAAQ,YAAY;AAAA,IAC1C;AAAA,EACF,CAAC;AACH;", "names": ["url"]}
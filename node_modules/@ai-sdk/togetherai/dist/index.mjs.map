{"version": 3, "sources": ["../src/togetherai-provider.ts", "../src/togetherai-image-model.ts"], "sourcesContent": ["import {\n  LanguageModelV1,\n  EmbeddingModelV1,\n  ProviderV1,\n  ImageModelV1,\n} from '@ai-sdk/provider';\nimport {\n  OpenAICompatibleChatLanguageModel,\n  OpenAICompatibleCompletionLanguageModel,\n  OpenAICompatibleEmbeddingModel,\n} from '@ai-sdk/openai-compatible';\nimport {\n  FetchFunction,\n  loadApiKey,\n  withoutTrailingSlash,\n} from '@ai-sdk/provider-utils';\nimport {\n  TogetherAIChatModelId,\n  TogetherAIChatSettings,\n} from './togetherai-chat-settings';\nimport {\n  TogetherAIEmbeddingModelId,\n  TogetherAIEmbeddingSettings,\n} from './togetherai-embedding-settings';\nimport {\n  TogetherAICompletionModelId,\n  TogetherAICompletionSettings,\n} from './togetherai-completion-settings';\nimport { TogetherAIImageModel } from './togetherai-image-model';\nimport {\n  TogetherAIImageModelId,\n  TogetherAIImageSettings,\n} from './togetherai-image-settings';\n\nexport interface TogetherAIProviderSettings {\n  /**\nTogetherAI API key.\n*/\n  apiKey?: string;\n  /**\nBase URL for the API calls.\n*/\n  baseURL?: string;\n  /**\nCustom headers to include in the requests.\n*/\n  headers?: Record<string, string>;\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n*/\n  fetch?: FetchFunction;\n}\n\nexport interface TogetherAIProvider extends ProviderV1 {\n  /**\nCreates a model for text generation.\n*/\n  (\n    modelId: TogetherAIChatModelId,\n    settings?: TogetherAIChatSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates a chat model for text generation.\n*/\n  chatModel(\n    modelId: TogetherAIChatModelId,\n    settings?: TogetherAIChatSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates a chat model for text generation.\n*/\n  languageModel(\n    modelId: TogetherAIChatModelId,\n    settings?: TogetherAIChatSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates a completion model for text generation.\n*/\n  completionModel(\n    modelId: TogetherAICompletionModelId,\n    settings?: TogetherAICompletionSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates a text embedding model for text generation.\n*/\n  textEmbeddingModel(\n    modelId: TogetherAIEmbeddingModelId,\n    settings?: TogetherAIEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n\n  /**\n  Creates a model for image generation.\n   */\n  image(\n    modelId: TogetherAIImageModelId,\n    settings?: TogetherAIImageSettings,\n  ): ImageModelV1;\n\n  /**\n  Creates a model for image generation.\n   */\n  imageModel(\n    modelId: TogetherAIImageModelId,\n    settings?: TogetherAIImageSettings,\n  ): ImageModelV1;\n}\n\nexport function createTogetherAI(\n  options: TogetherAIProviderSettings = {},\n): TogetherAIProvider {\n  const baseURL = withoutTrailingSlash(\n    options.baseURL ?? 'https://api.together.xyz/v1/',\n  );\n  const getHeaders = () => ({\n    Authorization: `Bearer ${loadApiKey({\n      apiKey: options.apiKey,\n      environmentVariableName: 'TOGETHER_AI_API_KEY',\n      description: 'TogetherAI',\n    })}`,\n    ...options.headers,\n  });\n\n  interface CommonModelConfig {\n    provider: string;\n    url: ({ path }: { path: string }) => string;\n    headers: () => Record<string, string>;\n    fetch?: FetchFunction;\n  }\n\n  const getCommonModelConfig = (modelType: string): CommonModelConfig => ({\n    provider: `togetherai.${modelType}`,\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    fetch: options.fetch,\n  });\n\n  const createChatModel = (\n    modelId: TogetherAIChatModelId,\n    settings: TogetherAIChatSettings = {},\n  ) => {\n    return new OpenAICompatibleChatLanguageModel(modelId, settings, {\n      ...getCommonModelConfig('chat'),\n      defaultObjectGenerationMode: 'tool',\n    });\n  };\n\n  const createCompletionModel = (\n    modelId: TogetherAICompletionModelId,\n    settings: TogetherAICompletionSettings = {},\n  ) =>\n    new OpenAICompatibleCompletionLanguageModel(\n      modelId,\n      settings,\n      getCommonModelConfig('completion'),\n    );\n\n  const createTextEmbeddingModel = (\n    modelId: TogetherAIEmbeddingModelId,\n    settings: TogetherAIEmbeddingSettings = {},\n  ) =>\n    new OpenAICompatibleEmbeddingModel(\n      modelId,\n      settings,\n      getCommonModelConfig('embedding'),\n    );\n\n  const createImageModel = (\n    modelId: TogetherAIImageModelId,\n    settings: TogetherAIImageSettings = {},\n  ) =>\n    new TogetherAIImageModel(modelId, settings, {\n      ...getCommonModelConfig('image'),\n      baseURL: baseURL ?? 'https://api.together.xyz/v1/',\n    });\n\n  const provider = (\n    modelId: TogetherAIChatModelId,\n    settings?: TogetherAIChatSettings,\n  ) => createChatModel(modelId, settings);\n\n  provider.completionModel = createCompletionModel;\n  provider.languageModel = createChatModel;\n  provider.chatModel = createChatModel;\n  provider.textEmbeddingModel = createTextEmbeddingModel;\n  provider.image = createImageModel;\n  provider.imageModel = createImageModel;\n\n  return provider;\n}\n\nexport const togetherai = createTogetherAI();\n", "import { ImageModelV1, ImageModelV1CallWarning } from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  create<PERSON>sonResponse<PERSON>and<PERSON>,\n  createJsonErrorResponse<PERSON>and<PERSON>,\n  FetchFunction,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport {\n  TogetherAIImageModelId,\n  TogetherAIImageSettings,\n} from './togetherai-image-settings';\nimport { z } from 'zod';\n\ninterface TogetherAIImageModelConfig {\n  provider: string;\n  baseURL: string;\n  headers: () => Record<string, string>;\n  fetch?: FetchFunction;\n  _internal?: {\n    currentDate?: () => Date;\n  };\n}\n\nexport class TogetherAIImageModel implements ImageModelV1 {\n  readonly specificationVersion = 'v1';\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  get maxImagesPerCall(): number {\n    return this.settings.maxImagesPerCall ?? 1;\n  }\n\n  constructor(\n    readonly modelId: TogetherAIImageModelId,\n    readonly settings: TogetherAIImageSettings,\n    private config: TogetherAIImageModelConfig,\n  ) {}\n\n  async doGenerate({\n    prompt,\n    n,\n    size,\n    seed,\n    providerOptions,\n    headers,\n    abortSignal,\n  }: Parameters<ImageModelV1['doGenerate']>[0]): Promise<\n    Awaited<ReturnType<ImageModelV1['doGenerate']>>\n  > {\n    const warnings: Array<ImageModelV1CallWarning> = [];\n\n    if (size != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'aspectRatio',\n        details:\n          'This model does not support the `aspectRatio` option. Use `size` instead.',\n      });\n    }\n\n    const currentDate = this.config._internal?.currentDate?.() ?? new Date();\n    const splitSize = size?.split('x');\n    // https://docs.together.ai/reference/post_images-generations\n    const { value: response, responseHeaders } = await postJsonToApi({\n      url: `${this.config.baseURL}/images/generations`,\n      headers: combineHeaders(this.config.headers(), headers),\n      body: {\n        model: this.modelId,\n        prompt,\n        seed,\n        n,\n        ...(splitSize && {\n          width: parseInt(splitSize[0]),\n          height: parseInt(splitSize[1]),\n        }),\n        response_format: 'base64',\n        ...(providerOptions.togetherai ?? {}),\n      },\n      failedResponseHandler: createJsonErrorResponseHandler({\n        errorSchema: togetheraiErrorSchema,\n        errorToMessage: data => data.error.message,\n      }),\n      successfulResponseHandler: createJsonResponseHandler(\n        togetheraiImageResponseSchema,\n      ),\n      abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    return {\n      images: response.data.map(item => item.b64_json),\n      warnings,\n      response: {\n        timestamp: currentDate,\n        modelId: this.modelId,\n        headers: responseHeaders,\n      },\n    };\n  }\n}\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst togetheraiImageResponseSchema = z.object({\n  data: z.array(\n    z.object({\n      b64_json: z.string(),\n    }),\n  ),\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst togetheraiErrorSchema = z.object({\n  error: z.object({\n    message: z.string(),\n  }),\n});\n"], "mappings": ";AAMA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP;AAAA,EAEE;AAAA,EACA;AAAA,OACK;;;ACdP;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,OACK;AAKP,SAAS,SAAS;AAYX,IAAM,uBAAN,MAAmD;AAAA,EAWxD,YACW,SACA,UACD,QACR;AAHS;AACA;AACD;AAbV,SAAS,uBAAuB;AAAA,EAc7B;AAAA,EAZH,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EAEA,IAAI,mBAA2B;AA/BjC;AAgCI,YAAO,UAAK,SAAS,qBAAd,YAAkC;AAAA,EAC3C;AAAA,EAQA,MAAM,WAAW;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAEE;AAnDJ;AAoDI,UAAM,WAA2C,CAAC;AAElD,QAAI,QAAQ,MAAM;AAChB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SACE;AAAA,MACJ,CAAC;AAAA,IACH;AAEA,UAAM,eAAc,sBAAK,OAAO,cAAZ,mBAAuB,gBAAvB,4CAA0C,oBAAI,KAAK;AACvE,UAAM,YAAY,6BAAM,MAAM;AAE9B,UAAM,EAAE,OAAO,UAAU,gBAAgB,IAAI,MAAM,cAAc;AAAA,MAC/D,KAAK,GAAG,KAAK,OAAO,OAAO;AAAA,MAC3B,SAAS,eAAe,KAAK,OAAO,QAAQ,GAAG,OAAO;AAAA,MACtD,MAAM;AAAA,QACJ,OAAO,KAAK;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA,GAAI,aAAa;AAAA,UACf,OAAO,SAAS,UAAU,CAAC,CAAC;AAAA,UAC5B,QAAQ,SAAS,UAAU,CAAC,CAAC;AAAA,QAC/B;AAAA,QACA,iBAAiB;AAAA,QACjB,IAAI,qBAAgB,eAAhB,YAA8B,CAAC;AAAA,MACrC;AAAA,MACA,uBAAuB,+BAA+B;AAAA,QACpD,aAAa;AAAA,QACb,gBAAgB,UAAQ,KAAK,MAAM;AAAA,MACrC,CAAC;AAAA,MACD,2BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA;AAAA,MACA,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,WAAO;AAAA,MACL,QAAQ,SAAS,KAAK,IAAI,UAAQ,KAAK,QAAQ;AAAA,MAC/C;AAAA,MACA,UAAU;AAAA,QACR,WAAW;AAAA,QACX,SAAS,KAAK;AAAA,QACd,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAM,gCAAgC,EAAE,OAAO;AAAA,EAC7C,MAAM,EAAE;AAAA,IACN,EAAE,OAAO;AAAA,MACP,UAAU,EAAE,OAAO;AAAA,IACrB,CAAC;AAAA,EACH;AACF,CAAC;AAID,IAAM,wBAAwB,EAAE,OAAO;AAAA,EACrC,OAAO,EAAE,OAAO;AAAA,IACd,SAAS,EAAE,OAAO;AAAA,EACpB,CAAC;AACH,CAAC;;;ADRM,SAAS,iBACd,UAAsC,CAAC,GACnB;AAlHtB;AAmHE,QAAM,UAAU;AAAA,KACd,aAAQ,YAAR,YAAmB;AAAA,EACrB;AACA,QAAM,aAAa,OAAO;AAAA,IACxB,eAAe,UAAU,WAAW;AAAA,MAClC,QAAQ,QAAQ;AAAA,MAChB,yBAAyB;AAAA,MACzB,aAAa;AAAA,IACf,CAAC,CAAC;AAAA,IACF,GAAG,QAAQ;AAAA,EACb;AASA,QAAM,uBAAuB,CAAC,eAA0C;AAAA,IACtE,UAAU,cAAc,SAAS;AAAA,IACjC,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,OAAO,GAAG,IAAI;AAAA,IACpC,SAAS;AAAA,IACT,OAAO,QAAQ;AAAA,EACjB;AAEA,QAAM,kBAAkB,CACtB,SACA,WAAmC,CAAC,MACjC;AACH,WAAO,IAAI,kCAAkC,SAAS,UAAU;AAAA,MAC9D,GAAG,qBAAqB,MAAM;AAAA,MAC9B,6BAA6B;AAAA,IAC/B,CAAC;AAAA,EACH;AAEA,QAAM,wBAAwB,CAC5B,SACA,WAAyC,CAAC,MAE1C,IAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,qBAAqB,YAAY;AAAA,EACnC;AAEF,QAAM,2BAA2B,CAC/B,SACA,WAAwC,CAAC,MAEzC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,qBAAqB,WAAW;AAAA,EAClC;AAEF,QAAM,mBAAmB,CACvB,SACA,WAAoC,CAAC,MAErC,IAAI,qBAAqB,SAAS,UAAU;AAAA,IAC1C,GAAG,qBAAqB,OAAO;AAAA,IAC/B,SAAS,4BAAW;AAAA,EACtB,CAAC;AAEH,QAAM,WAAW,CACf,SACA,aACG,gBAAgB,SAAS,QAAQ;AAEtC,WAAS,kBAAkB;AAC3B,WAAS,gBAAgB;AACzB,WAAS,YAAY;AACrB,WAAS,qBAAqB;AAC9B,WAAS,QAAQ;AACjB,WAAS,aAAa;AAEtB,SAAO;AACT;AAEO,IAAM,aAAa,iBAAiB;", "names": []}
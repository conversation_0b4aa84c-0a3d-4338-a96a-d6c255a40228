import { ProviderV1, LanguageModelV1, EmbeddingModelV1, ImageModelV1 } from '@ai-sdk/provider';
import { FetchFunction } from '@ai-sdk/provider-utils';
import { OpenAICompatibleChatSettings, OpenAICompatibleEmbeddingSettings, OpenAICompatibleCompletionSettings } from '@ai-sdk/openai-compatible';
export { OpenAICompatibleErrorData as TogetherAIErrorData } from '@ai-sdk/openai-compatible';

type TogetherAIChatModelId = 'meta-llama/Llama-3.3-70B-Instruct-Turbo' | 'meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo' | 'meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo' | 'meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo' | 'meta-llama/Meta-Llama-3-8B-Instruct-Turbo' | 'meta-llama/Meta-Llama-3-70B-Instruct-Turbo' | 'meta-llama/Llama-3.2-3B-Instruct-Turbo' | 'meta-llama/Meta-Llama-3-8B-Instruct-Lite' | 'meta-llama/Meta-Llama-3-70B-Instruct-Lite' | 'meta-llama/Llama-3-8b-chat-hf' | 'meta-llama/Llama-3-70b-chat-hf' | 'nvidia/Llama-3.1-Nemotron-70B-Instruct-HF' | 'Qwen/Qwen2.5-Coder-32B-Instruct' | 'Qwen/QwQ-32B-Preview' | 'microsoft/WizardLM-2-8x22B' | 'google/gemma-2-27b-it' | 'google/gemma-2-9b-it' | 'databricks/dbrx-instruct' | 'deepseek-ai/deepseek-llm-67b-chat' | 'deepseek-ai/DeepSeek-V3' | 'google/gemma-2b-it' | 'Gryphe/MythoMax-L2-13b' | 'meta-llama/Llama-2-13b-chat-hf' | 'mistralai/Mistral-7B-Instruct-v0.1' | 'mistralai/Mistral-7B-Instruct-v0.2' | 'mistralai/Mistral-7B-Instruct-v0.3' | 'mistralai/Mixtral-8x7B-Instruct-v0.1' | 'mistralai/Mixtral-8x22B-Instruct-v0.1' | 'NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO' | 'Qwen/Qwen2.5-7B-Instruct-Turbo' | 'Qwen/Qwen2.5-72B-Instruct-Turbo' | 'Qwen/Qwen2-72B-Instruct' | 'upstage/SOLAR-10.7B-Instruct-v1.0' | (string & {});
interface TogetherAIChatSettings extends OpenAICompatibleChatSettings {
}

type TogetherAIEmbeddingModelId = 'togethercomputer/m2-bert-80M-2k-retrieval' | 'togethercomputer/m2-bert-80M-32k-retrieval' | 'togethercomputer/m2-bert-80M-8k-retrieval' | 'WhereIsAI/UAE-Large-V1' | 'BAAI/bge-large-en-v1.5' | 'BAAI/bge-base-en-v1.5' | 'sentence-transformers/msmarco-bert-base-dot-v5' | 'bert-base-uncased' | (string & {});
interface TogetherAIEmbeddingSettings extends OpenAICompatibleEmbeddingSettings {
}

type TogetherAICompletionModelId = 'meta-llama/Llama-2-70b-hf' | 'mistralai/Mistral-7B-v0.1' | 'mistralai/Mixtral-8x7B-v0.1' | 'Meta-Llama/Llama-Guard-7b' | 'codellama/CodeLlama-34b-Instruct-hf' | 'Qwen/Qwen2.5-Coder-32B-Instruct' | (string & {});
interface TogetherAICompletionSettings extends OpenAICompatibleCompletionSettings {
}

type TogetherAIImageModelId = 'stabilityai/stable-diffusion-xl-base-1.0' | 'black-forest-labs/FLUX.1-dev' | 'black-forest-labs/FLUX.1-dev-lora' | 'black-forest-labs/FLUX.1-schnell' | 'black-forest-labs/FLUX.1-canny' | 'black-forest-labs/FLUX.1-depth' | 'black-forest-labs/FLUX.1-redux' | 'black-forest-labs/FLUX.1.1-pro' | 'black-forest-labs/FLUX.1-pro' | 'black-forest-labs/FLUX.1-schnell-Free' | (string & {});
interface TogetherAIImageSettings {
    /**
  Override the maximum number of images per call (default 1)
     */
    maxImagesPerCall?: number;
}

interface TogetherAIProviderSettings {
    /**
  TogetherAI API key.
  */
    apiKey?: string;
    /**
  Base URL for the API calls.
  */
    baseURL?: string;
    /**
  Custom headers to include in the requests.
  */
    headers?: Record<string, string>;
    /**
  Custom fetch implementation. You can use it as a middleware to intercept requests,
  or to provide a custom fetch implementation for e.g. testing.
  */
    fetch?: FetchFunction;
}
interface TogetherAIProvider extends ProviderV1 {
    /**
  Creates a model for text generation.
  */
    (modelId: TogetherAIChatModelId, settings?: TogetherAIChatSettings): LanguageModelV1;
    /**
  Creates a chat model for text generation.
  */
    chatModel(modelId: TogetherAIChatModelId, settings?: TogetherAIChatSettings): LanguageModelV1;
    /**
  Creates a chat model for text generation.
  */
    languageModel(modelId: TogetherAIChatModelId, settings?: TogetherAIChatSettings): LanguageModelV1;
    /**
  Creates a completion model for text generation.
  */
    completionModel(modelId: TogetherAICompletionModelId, settings?: TogetherAICompletionSettings): LanguageModelV1;
    /**
  Creates a text embedding model for text generation.
  */
    textEmbeddingModel(modelId: TogetherAIEmbeddingModelId, settings?: TogetherAIEmbeddingSettings): EmbeddingModelV1<string>;
    /**
    Creates a model for image generation.
     */
    image(modelId: TogetherAIImageModelId, settings?: TogetherAIImageSettings): ImageModelV1;
    /**
    Creates a model for image generation.
     */
    imageModel(modelId: TogetherAIImageModelId, settings?: TogetherAIImageSettings): ImageModelV1;
}
declare function createTogetherAI(options?: TogetherAIProviderSettings): TogetherAIProvider;
declare const togetherai: TogetherAIProvider;

export { type TogetherAIProvider, type TogetherAIProviderSettings, createTogetherAI, togetherai };

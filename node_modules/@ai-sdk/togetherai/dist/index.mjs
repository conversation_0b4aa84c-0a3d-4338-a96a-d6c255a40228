// src/togetherai-provider.ts
import {
  OpenAICompatibleChatLanguageModel,
  OpenAICompatibleCompletionLanguageModel,
  OpenAICompatibleEmbeddingModel
} from "@ai-sdk/openai-compatible";
import {
  loadApiKey,
  withoutTrailingSlash
} from "@ai-sdk/provider-utils";

// src/togetherai-image-model.ts
import {
  combineHeaders,
  createJsonResponseHandler,
  createJsonErrorResponseHandler,
  postJsonToApi
} from "@ai-sdk/provider-utils";
import { z } from "zod";
var TogetherAIImageModel = class {
  constructor(modelId, settings, config) {
    this.modelId = modelId;
    this.settings = settings;
    this.config = config;
    this.specificationVersion = "v1";
  }
  get provider() {
    return this.config.provider;
  }
  get maxImagesPerCall() {
    var _a;
    return (_a = this.settings.maxImagesPerCall) != null ? _a : 1;
  }
  async doGenerate({
    prompt,
    n,
    size,
    seed,
    providerOptions,
    headers,
    abortSignal
  }) {
    var _a, _b, _c, _d;
    const warnings = [];
    if (size != null) {
      warnings.push({
        type: "unsupported-setting",
        setting: "aspectRatio",
        details: "This model does not support the `aspectRatio` option. Use `size` instead."
      });
    }
    const currentDate = (_c = (_b = (_a = this.config._internal) == null ? void 0 : _a.currentDate) == null ? void 0 : _b.call(_a)) != null ? _c : /* @__PURE__ */ new Date();
    const splitSize = size == null ? void 0 : size.split("x");
    const { value: response, responseHeaders } = await postJsonToApi({
      url: `${this.config.baseURL}/images/generations`,
      headers: combineHeaders(this.config.headers(), headers),
      body: {
        model: this.modelId,
        prompt,
        seed,
        n,
        ...splitSize && {
          width: parseInt(splitSize[0]),
          height: parseInt(splitSize[1])
        },
        response_format: "base64",
        ...(_d = providerOptions.togetherai) != null ? _d : {}
      },
      failedResponseHandler: createJsonErrorResponseHandler({
        errorSchema: togetheraiErrorSchema,
        errorToMessage: (data) => data.error.message
      }),
      successfulResponseHandler: createJsonResponseHandler(
        togetheraiImageResponseSchema
      ),
      abortSignal,
      fetch: this.config.fetch
    });
    return {
      images: response.data.map((item) => item.b64_json),
      warnings,
      response: {
        timestamp: currentDate,
        modelId: this.modelId,
        headers: responseHeaders
      }
    };
  }
};
var togetheraiImageResponseSchema = z.object({
  data: z.array(
    z.object({
      b64_json: z.string()
    })
  )
});
var togetheraiErrorSchema = z.object({
  error: z.object({
    message: z.string()
  })
});

// src/togetherai-provider.ts
function createTogetherAI(options = {}) {
  var _a;
  const baseURL = withoutTrailingSlash(
    (_a = options.baseURL) != null ? _a : "https://api.together.xyz/v1/"
  );
  const getHeaders = () => ({
    Authorization: `Bearer ${loadApiKey({
      apiKey: options.apiKey,
      environmentVariableName: "TOGETHER_AI_API_KEY",
      description: "TogetherAI"
    })}`,
    ...options.headers
  });
  const getCommonModelConfig = (modelType) => ({
    provider: `togetherai.${modelType}`,
    url: ({ path }) => `${baseURL}${path}`,
    headers: getHeaders,
    fetch: options.fetch
  });
  const createChatModel = (modelId, settings = {}) => {
    return new OpenAICompatibleChatLanguageModel(modelId, settings, {
      ...getCommonModelConfig("chat"),
      defaultObjectGenerationMode: "tool"
    });
  };
  const createCompletionModel = (modelId, settings = {}) => new OpenAICompatibleCompletionLanguageModel(
    modelId,
    settings,
    getCommonModelConfig("completion")
  );
  const createTextEmbeddingModel = (modelId, settings = {}) => new OpenAICompatibleEmbeddingModel(
    modelId,
    settings,
    getCommonModelConfig("embedding")
  );
  const createImageModel = (modelId, settings = {}) => new TogetherAIImageModel(modelId, settings, {
    ...getCommonModelConfig("image"),
    baseURL: baseURL != null ? baseURL : "https://api.together.xyz/v1/"
  });
  const provider = (modelId, settings) => createChatModel(modelId, settings);
  provider.completionModel = createCompletionModel;
  provider.languageModel = createChatModel;
  provider.chatModel = createChatModel;
  provider.textEmbeddingModel = createTextEmbeddingModel;
  provider.image = createImageModel;
  provider.imageModel = createImageModel;
  return provider;
}
var togetherai = createTogetherAI();
export {
  createTogetherAI,
  togetherai
};
//# sourceMappingURL=index.mjs.map
{"version": 3, "sources": ["../src/index.ts", "../src/use-assistant.ts", "../src/use-chat.ts", "../src/throttle.ts", "../src/util/use-stable-value.ts", "../src/use-completion.ts", "../src/use-object.ts"], "sourcesContent": ["export * from './use-assistant';\nexport * from './use-chat';\nexport * from './use-completion';\nexport * from './use-object';\n", "import { isAbortError } from '@ai-sdk/provider-utils';\nimport {\n  AssistantStatus,\n  CreateMessage,\n  Message,\n  UseAssistantOptions,\n  generateId,\n  processAssistantStream,\n} from '@ai-sdk/ui-utils';\nimport { useCallback, useRef, useState } from 'react';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => fetch;\n\nexport type UseAssistantHelpers = {\n  /**\n   * The current array of chat messages.\n   */\n  messages: Message[];\n\n  /**\n   * Update the message store with a new array of messages.\n   */\n  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;\n\n  /**\n   * The current thread ID.\n   */\n  threadId: string | undefined;\n\n  /**\n   * Set the current thread ID. Specifying a thread ID will switch to that thread, if it exists. If set to 'undefined', a new thread will be created. For both cases, `threadId` will be updated with the new value and `messages` will be cleared.\n   */\n  setThreadId: (threadId: string | undefined) => void;\n\n  /**\n   * The current value of the input field.\n   */\n  input: string;\n\n  /**\n   * Append a user message to the chat list. This triggers the API call to fetch\n   * the assistant's response.\n   * @param message The message to append\n   * @param requestOptions Additional options to pass to the API call\n   */\n  append: (\n    message: Message | CreateMessage,\n    requestOptions?: {\n      data?: Record<string, string>;\n    },\n  ) => Promise<void>;\n\n  /**\nAbort the current request immediately, keep the generated tokens if any.\n   */\n  stop: () => void;\n\n  /**\n   * setState-powered method to update the input value.\n   */\n  setInput: React.Dispatch<React.SetStateAction<string>>;\n\n  /**\n   * Handler for the `onChange` event of the input field to control the input's value.\n   */\n  handleInputChange: (\n    event:\n      | React.ChangeEvent<HTMLInputElement>\n      | React.ChangeEvent<HTMLTextAreaElement>,\n  ) => void;\n\n  /**\n   * Form submission handler that automatically resets the input field and appends a user message.\n   */\n  submitMessage: (\n    event?: React.FormEvent<HTMLFormElement>,\n    requestOptions?: {\n      data?: Record<string, string>;\n    },\n  ) => Promise<void>;\n\n  /**\n   * The current status of the assistant. This can be used to show a loading indicator.\n   */\n  status: AssistantStatus;\n\n  /**\n   * The error thrown during the assistant message processing, if any.\n   */\n  error: undefined | Error;\n};\n\nexport function useAssistant({\n  api,\n  threadId: threadIdParam,\n  credentials,\n  headers,\n  body,\n  onError,\n  fetch,\n}: UseAssistantOptions): UseAssistantHelpers {\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [input, setInput] = useState('');\n  const [currentThreadId, setCurrentThreadId] = useState<string | undefined>(\n    undefined,\n  );\n  const [status, setStatus] = useState<AssistantStatus>('awaiting_message');\n  const [error, setError] = useState<undefined | Error>(undefined);\n\n  const handleInputChange = (\n    event:\n      | React.ChangeEvent<HTMLInputElement>\n      | React.ChangeEvent<HTMLTextAreaElement>,\n  ) => {\n    setInput(event.target.value);\n  };\n\n  // Abort controller to cancel the current API call.\n  const abortControllerRef = useRef<AbortController | null>(null);\n\n  const stop = useCallback(() => {\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n      abortControllerRef.current = null;\n    }\n  }, []);\n\n  const append = async (\n    message: Message | CreateMessage,\n    requestOptions?: {\n      data?: Record<string, string>;\n    },\n  ) => {\n    setStatus('in_progress');\n\n    setMessages(messages => [\n      ...messages,\n      {\n        ...message,\n        id: message.id ?? generateId(),\n      },\n    ]);\n\n    setInput('');\n\n    const abortController = new AbortController();\n\n    try {\n      abortControllerRef.current = abortController;\n\n      const actualFetch = fetch ?? getOriginalFetch();\n      const response = await actualFetch(api, {\n        method: 'POST',\n        credentials,\n        signal: abortController.signal,\n        headers: { 'Content-Type': 'application/json', ...headers },\n        body: JSON.stringify({\n          ...body,\n          // always use user-provided threadId when available:\n          threadId: threadIdParam ?? currentThreadId ?? null,\n          message: message.content,\n\n          // optional request data:\n          data: requestOptions?.data,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(\n          (await response.text()) ?? 'Failed to fetch the assistant response.',\n        );\n      }\n\n      if (response.body == null) {\n        throw new Error('The response body is empty.');\n      }\n\n      await processAssistantStream({\n        stream: response.body,\n        onAssistantMessagePart(value) {\n          setMessages(messages => [\n            ...messages,\n            {\n              id: value.id,\n              role: value.role,\n              content: value.content[0].text.value,\n              parts: [],\n            },\n          ]);\n        },\n        onTextPart(value) {\n          // text delta - add to last message:\n          setMessages(messages => {\n            const lastMessage = messages[messages.length - 1];\n            return [\n              ...messages.slice(0, messages.length - 1),\n              {\n                id: lastMessage.id,\n                role: lastMessage.role,\n                content: lastMessage.content + value,\n                parts: lastMessage.parts,\n              },\n            ];\n          });\n        },\n        onAssistantControlDataPart(value) {\n          setCurrentThreadId(value.threadId);\n\n          // set id of last message:\n          setMessages(messages => {\n            const lastMessage = messages[messages.length - 1];\n            lastMessage.id = value.messageId;\n            return [...messages.slice(0, messages.length - 1), lastMessage];\n          });\n        },\n        onDataMessagePart(value) {\n          setMessages(messages => [\n            ...messages,\n            {\n              id: value.id ?? generateId(),\n              role: 'data',\n              content: '',\n              data: value.data,\n              parts: [],\n            },\n          ]);\n        },\n        onErrorPart(value) {\n          setError(new Error(value));\n        },\n      });\n    } catch (error) {\n      // Ignore abort errors as they are expected when the user cancels the request:\n      if (isAbortError(error) && abortController.signal.aborted) {\n        abortControllerRef.current = null;\n        return;\n      }\n\n      if (onError && error instanceof Error) {\n        onError(error);\n      }\n\n      setError(error as Error);\n    } finally {\n      abortControllerRef.current = null;\n      setStatus('awaiting_message');\n    }\n  };\n\n  const submitMessage = async (\n    event?: React.FormEvent<HTMLFormElement>,\n    requestOptions?: {\n      data?: Record<string, string>;\n    },\n  ) => {\n    event?.preventDefault?.();\n\n    if (input === '') {\n      return;\n    }\n\n    append({ role: 'user', content: input, parts: [] }, requestOptions);\n  };\n\n  const setThreadId = (threadId: string | undefined) => {\n    setCurrentThreadId(threadId);\n    setMessages([]);\n  };\n\n  return {\n    append,\n    messages,\n    setMessages,\n    threadId: currentThreadId,\n    setThreadId,\n    input,\n    setInput,\n    handleInputChange,\n    submitMessage,\n    status,\n    error,\n    stop,\n  };\n}\n", "import type {\n  ChatRequest,\n  ChatRequestOptions,\n  CreateMessage,\n  JSONValue,\n  Message,\n  UIMessage,\n  UseChatOptions,\n} from '@ai-sdk/ui-utils';\nimport {\n  callChatApi,\n  extractMaxToolInvocationStep,\n  fillMessageParts,\n  generateId as generateIdFunc,\n  getMessageParts,\n  isAssistantMessageWithCompletedToolCalls,\n  prepareAttachmentsForRequest,\n  shouldResubmitMessages,\n  updateToolCallResult,\n} from '@ai-sdk/ui-utils';\nimport { useCallback, useEffect, useMemo, useRef, useState } from 'react';\nimport useSWR from 'swr';\nimport { throttle } from './throttle';\nimport { useStableValue } from './util/use-stable-value';\n\nexport type { CreateMessage, Message, UseChatOptions };\n\nexport type UseChatHelpers = {\n  /** Current messages in the chat */\n  messages: UIMessage[];\n  /** The error object of the API request */\n  error: undefined | Error;\n  /**\n   * Append a user message to the chat list. This triggers the API call to fetch\n   * the assistant's response.\n   * @param message The message to append\n   * @param options Additional options to pass to the API call\n   */\n  append: (\n    message: Message | CreateMessage,\n    chatRequestOptions?: ChatRequestOptions,\n  ) => Promise<string | null | undefined>;\n  /**\n   * Reload the last AI chat response for the given chat history. If the last\n   * message isn't from the assistant, it will request the API to generate a\n   * new response.\n   */\n  reload: (\n    chatRequestOptions?: ChatRequestOptions,\n  ) => Promise<string | null | undefined>;\n  /**\n   * Abort the current request immediately, keep the generated tokens if any.\n   */\n  stop: () => void;\n\n  /**\n   * Resume an ongoing chat generation stream. This does not resume an aborted generation.\n   */\n  experimental_resume: () => void;\n\n  /**\n   * Update the `messages` state locally. This is useful when you want to\n   * edit the messages on the client, and then trigger the `reload` method\n   * manually to regenerate the AI response.\n   */\n  setMessages: (\n    messages: Message[] | ((messages: Message[]) => Message[]),\n  ) => void;\n  /** The current value of the input */\n  input: string;\n  /** setState-powered method to update the input value */\n  setInput: React.Dispatch<React.SetStateAction<string>>;\n  /** An input/textarea-ready onChange handler to control the value of the input */\n  handleInputChange: (\n    e:\n      | React.ChangeEvent<HTMLInputElement>\n      | React.ChangeEvent<HTMLTextAreaElement>,\n  ) => void;\n  /** Form submission handler to automatically reset input and append a user message */\n  handleSubmit: (\n    event?: { preventDefault?: () => void },\n    chatRequestOptions?: ChatRequestOptions,\n  ) => void;\n  metadata?: Object;\n\n  /**\n   * Whether the API request is in progress\n   *\n   * @deprecated use `status` instead\n   */\n  isLoading: boolean;\n\n  /**\n   * Hook status:\n   *\n   * - `submitted`: The message has been sent to the API and we're awaiting the start of the response stream.\n   * - `streaming`: The response is actively streaming in from the API, receiving chunks of data.\n   * - `ready`: The full response has been received and processed; a new user message can be submitted.\n   * - `error`: An error occurred during the API request, preventing successful completion.\n   */\n  status: 'submitted' | 'streaming' | 'ready' | 'error';\n\n  /** Additional data added on the server via StreamData. */\n  data?: JSONValue[];\n\n  /** Set the data of the chat. You can use this to transform or clear the chat data. */\n  setData: (\n    data:\n      | JSONValue[]\n      | undefined\n      | ((data: JSONValue[] | undefined) => JSONValue[] | undefined),\n  ) => void;\n\n  /** The id of the chat */\n  id: string;\n};\n\nexport function useChat({\n  api = '/api/chat',\n  id,\n  initialMessages,\n  initialInput = '',\n  sendExtraMessageFields,\n  onToolCall,\n  experimental_prepareRequestBody,\n  maxSteps = 1,\n  streamProtocol = 'data',\n  onResponse,\n  onFinish,\n  onError,\n  credentials,\n  headers,\n  body,\n  generateId = generateIdFunc,\n  fetch,\n  keepLastMessageOnError = true,\n  experimental_throttle: throttleWaitMs,\n}: UseChatOptions & {\n  key?: string;\n\n  /**\n   * Experimental (React only). When a function is provided, it will be used\n   * to prepare the request body for the chat API. This can be useful for\n   * customizing the request body based on the messages and data in the chat.\n   *\n   * @param messages The current messages in the chat.\n   * @param requestData The data object passed in the chat request.\n   * @param requestBody The request body object passed in the chat request.\n   */\n  experimental_prepareRequestBody?: (options: {\n    id: string;\n    messages: UIMessage[];\n    requestData?: JSONValue;\n    requestBody?: object;\n  }) => unknown;\n\n  /**\nCustom throttle wait in ms for the chat messages and data updates.\nDefault is undefined, which disables throttling.\n   */\n  experimental_throttle?: number;\n\n  /**\nMaximum number of sequential LLM calls (steps), e.g. when you use tool calls.\nMust be at least 1.\n\nA maximum number is required to prevent infinite loops in the case of misconfigured tools.\n\nBy default, it's set to 1, which means that only a single LLM call is made.\n */\n  maxSteps?: number;\n} = {}): UseChatHelpers & {\n  addToolResult: ({\n    toolCallId,\n    result,\n  }: {\n    toolCallId: string;\n    result: any;\n  }) => void;\n} {\n  // Generate ID once, store in state for stability across re-renders\n  const [hookId] = useState(generateId);\n\n  // Use the caller-supplied ID if available; otherwise, fall back to our stable ID\n  const chatId = id ?? hookId;\n  const chatKey = typeof api === 'string' ? [api, chatId] : chatId;\n\n  // Store array of the processed initial messages to avoid re-renders:\n  const stableInitialMessages = useStableValue(initialMessages ?? []);\n  const processedInitialMessages = useMemo(\n    () => fillMessageParts(stableInitialMessages),\n    [stableInitialMessages],\n  );\n\n  // Store the chat state in SWR, using the chatId as the key to share states.\n  const { data: messages, mutate } = useSWR<UIMessage[]>(\n    [chatKey, 'messages'],\n    null,\n    { fallbackData: processedInitialMessages },\n  );\n\n  // Keep the latest messages in a ref.\n  const messagesRef = useRef<UIMessage[]>(messages || []);\n  useEffect(() => {\n    messagesRef.current = messages || [];\n  }, [messages]);\n\n  // stream data\n  const { data: streamData, mutate: mutateStreamData } = useSWR<\n    JSONValue[] | undefined\n  >([chatKey, 'streamData'], null);\n\n  // keep the latest stream data in a ref\n  const streamDataRef = useRef<JSONValue[] | undefined>(streamData);\n  useEffect(() => {\n    streamDataRef.current = streamData;\n  }, [streamData]);\n\n  const { data: status = 'ready', mutate: mutateStatus } = useSWR<\n    'submitted' | 'streaming' | 'ready' | 'error'\n  >([chatKey, 'status'], null);\n\n  const { data: error = undefined, mutate: setError } = useSWR<\n    undefined | Error\n  >([chatKey, 'error'], null);\n\n  // Abort controller to cancel the current API call.\n  const abortControllerRef = useRef<AbortController | null>(null);\n\n  const extraMetadataRef = useRef({\n    credentials,\n    headers,\n    body,\n  });\n\n  useEffect(() => {\n    extraMetadataRef.current = {\n      credentials,\n      headers,\n      body,\n    };\n  }, [credentials, headers, body]);\n\n  const triggerRequest = useCallback(\n    async (\n      chatRequest: ChatRequest,\n      requestType: 'generate' | 'resume' = 'generate',\n    ) => {\n      mutateStatus('submitted');\n      setError(undefined);\n\n      const chatMessages = fillMessageParts(chatRequest.messages);\n\n      const messageCount = chatMessages.length;\n      const maxStep = extractMaxToolInvocationStep(\n        chatMessages[chatMessages.length - 1]?.toolInvocations,\n      );\n\n      try {\n        const abortController = new AbortController();\n        abortControllerRef.current = abortController;\n\n        const throttledMutate = throttle(mutate, throttleWaitMs);\n        const throttledMutateStreamData = throttle(\n          mutateStreamData,\n          throttleWaitMs,\n        );\n\n        // Do an optimistic update to the chat state to show the updated messages immediately:\n        const previousMessages = messagesRef.current;\n        throttledMutate(chatMessages, false);\n\n        const constructedMessagesPayload = sendExtraMessageFields\n          ? chatMessages\n          : chatMessages.map(\n              ({\n                role,\n                content,\n                experimental_attachments,\n                data,\n                annotations,\n                toolInvocations,\n                parts,\n              }) => ({\n                role,\n                content,\n                ...(experimental_attachments !== undefined && {\n                  experimental_attachments,\n                }),\n                ...(data !== undefined && { data }),\n                ...(annotations !== undefined && { annotations }),\n                ...(toolInvocations !== undefined && { toolInvocations }),\n                ...(parts !== undefined && { parts }),\n              }),\n            );\n\n        const existingData = streamDataRef.current;\n\n        await callChatApi({\n          api,\n          body: experimental_prepareRequestBody?.({\n            id: chatId,\n            messages: chatMessages,\n            requestData: chatRequest.data,\n            requestBody: chatRequest.body,\n          }) ?? {\n            id: chatId,\n            messages: constructedMessagesPayload,\n            data: chatRequest.data,\n            ...extraMetadataRef.current.body,\n            ...chatRequest.body,\n          },\n          streamProtocol,\n          credentials: extraMetadataRef.current.credentials,\n          headers: {\n            ...extraMetadataRef.current.headers,\n            ...chatRequest.headers,\n          },\n          abortController: () => abortControllerRef.current,\n          restoreMessagesOnFailure() {\n            if (!keepLastMessageOnError) {\n              throttledMutate(previousMessages, false);\n            }\n          },\n          onResponse,\n          onUpdate({ message, data, replaceLastMessage }) {\n            mutateStatus('streaming');\n\n            throttledMutate(\n              [\n                ...(replaceLastMessage\n                  ? chatMessages.slice(0, chatMessages.length - 1)\n                  : chatMessages),\n                message,\n              ],\n              false,\n            );\n\n            if (data?.length) {\n              throttledMutateStreamData(\n                [...(existingData ?? []), ...data],\n                false,\n              );\n            }\n          },\n          onToolCall,\n          onFinish,\n          generateId,\n          fetch,\n          lastMessage: chatMessages[chatMessages.length - 1],\n          requestType,\n        });\n\n        abortControllerRef.current = null;\n\n        mutateStatus('ready');\n      } catch (err) {\n        // Ignore abort errors as they are expected.\n        if ((err as any).name === 'AbortError') {\n          abortControllerRef.current = null;\n          mutateStatus('ready');\n          return null;\n        }\n\n        if (onError && err instanceof Error) {\n          onError(err);\n        }\n\n        setError(err as Error);\n        mutateStatus('error');\n      }\n\n      // auto-submit when all tool calls in the last assistant message have results\n      // and assistant has not answered yet\n      const messages = messagesRef.current;\n      if (\n        shouldResubmitMessages({\n          originalMaxToolInvocationStep: maxStep,\n          originalMessageCount: messageCount,\n          maxSteps,\n          messages,\n        })\n      ) {\n        await triggerRequest({ messages });\n      }\n    },\n    [\n      mutate,\n      mutateStatus,\n      api,\n      extraMetadataRef,\n      onResponse,\n      onFinish,\n      onError,\n      setError,\n      mutateStreamData,\n      streamDataRef,\n      streamProtocol,\n      sendExtraMessageFields,\n      experimental_prepareRequestBody,\n      onToolCall,\n      maxSteps,\n      messagesRef,\n      abortControllerRef,\n      generateId,\n      fetch,\n      keepLastMessageOnError,\n      throttleWaitMs,\n      chatId,\n    ],\n  );\n\n  const append = useCallback(\n    async (\n      message: Message | CreateMessage,\n      {\n        data,\n        headers,\n        body,\n        experimental_attachments = message.experimental_attachments,\n      }: ChatRequestOptions = {},\n    ) => {\n      const attachmentsForRequest = await prepareAttachmentsForRequest(\n        experimental_attachments,\n      );\n\n      const messages = messagesRef.current.concat({\n        ...message,\n        id: message.id ?? generateId(),\n        createdAt: message.createdAt ?? new Date(),\n        experimental_attachments:\n          attachmentsForRequest.length > 0 ? attachmentsForRequest : undefined,\n        parts: getMessageParts(message),\n      });\n\n      return triggerRequest({ messages, headers, body, data });\n    },\n    [triggerRequest, generateId],\n  );\n\n  const reload = useCallback(\n    async ({ data, headers, body }: ChatRequestOptions = {}) => {\n      const messages = messagesRef.current;\n\n      if (messages.length === 0) {\n        return null;\n      }\n\n      // Remove last assistant message and retry last user message.\n      const lastMessage = messages[messages.length - 1];\n      return triggerRequest({\n        messages:\n          lastMessage.role === 'assistant' ? messages.slice(0, -1) : messages,\n        headers,\n        body,\n        data,\n      });\n    },\n    [triggerRequest],\n  );\n\n  const stop = useCallback(() => {\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n      abortControllerRef.current = null;\n    }\n  }, []);\n\n  const experimental_resume = useCallback(async () => {\n    const messages = messagesRef.current;\n\n    triggerRequest({ messages }, 'resume');\n  }, [triggerRequest]);\n\n  const setMessages = useCallback(\n    (messages: Message[] | ((messages: Message[]) => Message[])) => {\n      if (typeof messages === 'function') {\n        messages = messages(messagesRef.current);\n      }\n\n      const messagesWithParts = fillMessageParts(messages);\n      mutate(messagesWithParts, false);\n      messagesRef.current = messagesWithParts;\n    },\n    [mutate],\n  );\n\n  const setData = useCallback(\n    (\n      data:\n        | JSONValue[]\n        | undefined\n        | ((data: JSONValue[] | undefined) => JSONValue[] | undefined),\n    ) => {\n      if (typeof data === 'function') {\n        data = data(streamDataRef.current);\n      }\n\n      mutateStreamData(data, false);\n      streamDataRef.current = data;\n    },\n    [mutateStreamData],\n  );\n\n  // Input state and handlers.\n  const [input, setInput] = useState(initialInput);\n\n  const handleSubmit = useCallback(\n    async (\n      event?: { preventDefault?: () => void },\n      options: ChatRequestOptions = {},\n      metadata?: Object,\n    ) => {\n      event?.preventDefault?.();\n\n      if (!input && !options.allowEmptySubmit) return;\n\n      if (metadata) {\n        extraMetadataRef.current = {\n          ...extraMetadataRef.current,\n          ...metadata,\n        };\n      }\n\n      const attachmentsForRequest = await prepareAttachmentsForRequest(\n        options.experimental_attachments,\n      );\n\n      const messages = messagesRef.current.concat({\n        id: generateId(),\n        createdAt: new Date(),\n        role: 'user',\n        content: input,\n        experimental_attachments:\n          attachmentsForRequest.length > 0 ? attachmentsForRequest : undefined,\n        parts: [{ type: 'text', text: input }],\n      });\n\n      const chatRequest: ChatRequest = {\n        messages,\n        headers: options.headers,\n        body: options.body,\n        data: options.data,\n      };\n\n      triggerRequest(chatRequest);\n\n      setInput('');\n    },\n    [input, generateId, triggerRequest],\n  );\n\n  const handleInputChange = (e: any) => {\n    setInput(e.target.value);\n  };\n\n  const addToolResult = useCallback(\n    ({ toolCallId, result }: { toolCallId: string; result: unknown }) => {\n      const currentMessages = messagesRef.current;\n\n      updateToolCallResult({\n        messages: currentMessages,\n        toolCallId,\n        toolResult: result,\n      });\n\n      // array mutation is required to trigger a re-render\n      mutate(\n        [\n          ...currentMessages.slice(0, currentMessages.length - 1),\n          { ...currentMessages[currentMessages.length - 1] },\n        ],\n        false,\n      );\n\n      // when the request is ongoing, the auto-submit will be triggered after the request is finished\n      if (status === 'submitted' || status === 'streaming') {\n        return;\n      }\n\n      // auto-submit when all tool calls in the last assistant message have results:\n      const lastMessage = currentMessages[currentMessages.length - 1];\n      if (isAssistantMessageWithCompletedToolCalls(lastMessage)) {\n        triggerRequest({ messages: currentMessages });\n      }\n    },\n    [mutate, status, triggerRequest],\n  );\n\n  return {\n    messages: messages ?? [],\n    id: chatId,\n    setMessages,\n    data: streamData,\n    setData,\n    error,\n    append,\n    reload,\n    stop,\n    experimental_resume,\n    input,\n    setInput,\n    handleInputChange,\n    handleSubmit,\n    isLoading: status === 'submitted' || status === 'streaming',\n    status,\n    addToolResult,\n  };\n}\n", "import throttleFunction from 'throttleit';\n\nexport function throttle<T extends (...args: any[]) => any>(\n  fn: T,\n  waitMs: number | undefined,\n): T {\n  return waitMs != null ? throttleFunction(fn, waitMs) : fn;\n}\n", "import { isDeepEqualData } from '@ai-sdk/ui-utils';\nimport { useEffect, useState } from 'react';\n\n/**\n * Returns a stable value that only updates the stored value (and triggers a re-render)\n * when the value's contents differ by deep-compare.\n */\nexport function useStableValue<T>(latestValue: T): T {\n  const [value, setValue] = useState<T>(latestValue);\n\n  useEffect(() => {\n    if (!isDeepEqualData(latestValue, value)) {\n      setValue(latestValue);\n    }\n  }, [latestValue, value]);\n\n  return value;\n}\n", "import {\n  JSONValue,\n  RequestOptions,\n  UseCompletionOptions,\n  callCompletionApi,\n} from '@ai-sdk/ui-utils';\nimport { useCallback, useEffect, useId, useRef, useState } from 'react';\nimport useSWR from 'swr';\nimport { throttle } from './throttle';\n\nexport type { UseCompletionOptions };\n\nexport type UseCompletionHelpers = {\n  /** The current completion result */\n  completion: string;\n  /**\n   * Send a new prompt to the API endpoint and update the completion state.\n   */\n  complete: (\n    prompt: string,\n    options?: RequestOptions,\n  ) => Promise<string | null | undefined>;\n  /** The error object of the API request */\n  error: undefined | Error;\n  /**\n   * Abort the current API request but keep the generated tokens.\n   */\n  stop: () => void;\n  /**\n   * Update the `completion` state locally.\n   */\n  setCompletion: (completion: string) => void;\n  /** The current value of the input */\n  input: string;\n  /** setState-powered method to update the input value */\n  setInput: React.Dispatch<React.SetStateAction<string>>;\n  /**\n   * An input/textarea-ready onChange handler to control the value of the input\n   * @example\n   * ```jsx\n   * <input onChange={handleInputChange} value={input} />\n   * ```\n   */\n  handleInputChange: (\n    event:\n      | React.ChangeEvent<HTMLInputElement>\n      | React.ChangeEvent<HTMLTextAreaElement>,\n  ) => void;\n\n  /**\n   * Form submission handler to automatically reset input and append a user message\n   * @example\n   * ```jsx\n   * <form onSubmit={handleSubmit}>\n   *  <input onChange={handleInputChange} value={input} />\n   * </form>\n   * ```\n   */\n  handleSubmit: (event?: { preventDefault?: () => void }) => void;\n\n  /** Whether the API request is in progress */\n  isLoading: boolean;\n  /** Additional data added on the server via StreamData */\n  data?: JSONValue[];\n};\n\nexport function useCompletion({\n  api = '/api/completion',\n  id,\n  initialCompletion = '',\n  initialInput = '',\n  credentials,\n  headers,\n  body,\n  streamProtocol = 'data',\n  fetch,\n  onResponse,\n  onFinish,\n  onError,\n  experimental_throttle: throttleWaitMs,\n}: UseCompletionOptions & {\n  /**\n   * Custom throttle wait in ms for the completion and data updates.\n   * Default is undefined, which disables throttling.\n   */\n  experimental_throttle?: number;\n} = {}): UseCompletionHelpers {\n  // Generate an unique id for the completion if not provided.\n  const hookId = useId();\n  const completionId = id || hookId;\n\n  // Store the completion state in SWR, using the completionId as the key to share states.\n  const { data, mutate } = useSWR<string>([api, completionId], null, {\n    fallbackData: initialCompletion,\n  });\n\n  const { data: isLoading = false, mutate: mutateLoading } = useSWR<boolean>(\n    [completionId, 'loading'],\n    null,\n  );\n\n  const { data: streamData, mutate: mutateStreamData } = useSWR<\n    JSONValue[] | undefined\n  >([completionId, 'streamData'], null);\n\n  const [error, setError] = useState<undefined | Error>(undefined);\n  const completion = data!;\n\n  // Abort controller to cancel the current API call.\n  const [abortController, setAbortController] =\n    useState<AbortController | null>(null);\n\n  const extraMetadataRef = useRef({\n    credentials,\n    headers,\n    body,\n  });\n\n  useEffect(() => {\n    extraMetadataRef.current = {\n      credentials,\n      headers,\n      body,\n    };\n  }, [credentials, headers, body]);\n\n  const triggerRequest = useCallback(\n    async (prompt: string, options?: RequestOptions) =>\n      callCompletionApi({\n        api,\n        prompt,\n        credentials: extraMetadataRef.current.credentials,\n        headers: { ...extraMetadataRef.current.headers, ...options?.headers },\n        body: {\n          ...extraMetadataRef.current.body,\n          ...options?.body,\n        },\n        streamProtocol,\n        fetch,\n        // throttle streamed ui updates:\n        setCompletion: throttle(\n          (completion: string) => mutate(completion, false),\n          throttleWaitMs,\n        ),\n        onData: throttle(\n          (data: JSONValue[]) =>\n            mutateStreamData([...(streamData ?? []), ...(data ?? [])], false),\n          throttleWaitMs,\n        ),\n        setLoading: mutateLoading,\n        setError,\n        setAbortController,\n        onResponse,\n        onFinish,\n        onError,\n      }),\n    [\n      mutate,\n      mutateLoading,\n      api,\n      extraMetadataRef,\n      setAbortController,\n      onResponse,\n      onFinish,\n      onError,\n      setError,\n      streamData,\n      streamProtocol,\n      fetch,\n      mutateStreamData,\n      throttleWaitMs,\n    ],\n  );\n\n  const stop = useCallback(() => {\n    if (abortController) {\n      abortController.abort();\n      setAbortController(null);\n    }\n  }, [abortController]);\n\n  const setCompletion = useCallback(\n    (completion: string) => {\n      mutate(completion, false);\n    },\n    [mutate],\n  );\n\n  const complete = useCallback<UseCompletionHelpers['complete']>(\n    async (prompt, options) => {\n      return triggerRequest(prompt, options);\n    },\n    [triggerRequest],\n  );\n\n  const [input, setInput] = useState(initialInput);\n\n  const handleSubmit = useCallback(\n    (event?: { preventDefault?: () => void }) => {\n      event?.preventDefault?.();\n      return input ? complete(input) : undefined;\n    },\n    [input, complete],\n  );\n\n  const handleInputChange = useCallback(\n    (e: any) => {\n      setInput(e.target.value);\n    },\n    [setInput],\n  );\n\n  return {\n    completion,\n    complete,\n    error,\n    setCompletion,\n    stop,\n    input,\n    setInput,\n    handleInputChange,\n    handleSubmit,\n    isLoading,\n    data: streamData,\n  };\n}\n", "import {\n  FetchFunction,\n  isAbortError,\n  safeValidateTypes,\n} from '@ai-sdk/provider-utils';\nimport {\n  asSchema,\n  DeepPartial,\n  isDeepEqualData,\n  parsePartialJson,\n  Schema,\n} from '@ai-sdk/ui-utils';\nimport { useCallback, useId, useRef, useState } from 'react';\nimport useSWR from 'swr';\nimport z from 'zod';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => fetch;\n\nexport type Experimental_UseObjectOptions<RESULT> = {\n  /**\n   * The API endpoint. It should stream JSON that matches the schema as chunked text.\n   */\n  api: string;\n\n  /**\n   * A Zod schema that defines the shape of the complete object.\n   */\n  schema: z.Schema<RESULT, z.ZodTypeDef, any> | Schema<RESULT>;\n\n  /**\n   * An unique identifier. If not provided, a random one will be\n   * generated. When provided, the `useObject` hook with the same `id` will\n   * have shared states across components.\n   */\n  id?: string;\n\n  /**\n   * An optional value for the initial object.\n   */\n  initialValue?: DeepPartial<RESULT>;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n    */\n  fetch?: FetchFunction;\n\n  /**\nCallback that is called when the stream has finished.\n     */\n  onFinish?: (event: {\n    /**\nThe generated object (typed according to the schema).\nCan be undefined if the final object does not match the schema.\n   */\n    object: RESULT | undefined;\n\n    /**\nOptional error object. This is e.g. a TypeValidationError when the final object does not match the schema.\n */\n    error: Error | undefined;\n  }) => Promise<void> | void;\n\n  /**\n   * Callback function to be called when an error is encountered.\n   */\n  onError?: (error: Error) => void;\n\n  /**\n   * Additional HTTP headers to be included in the request.\n   */\n  headers?: Record<string, string> | Headers;\n\n  /**\n   * The credentials mode to be used for the fetch request.\n   * Possible values are: 'omit', 'same-origin', 'include'.\n   * Defaults to 'same-origin'.\n   */\n  credentials?: RequestCredentials;\n};\n\nexport type Experimental_UseObjectHelpers<RESULT, INPUT> = {\n  /**\n   * Calls the API with the provided input as JSON body.\n   */\n  submit: (input: INPUT) => void;\n\n  /**\n   * The current value for the generated object. Updated as the API streams JSON chunks.\n   */\n  object: DeepPartial<RESULT> | undefined;\n\n  /**\n   * The error object of the API request if any.\n   */\n  error: Error | undefined;\n\n  /**\n   * Flag that indicates whether an API request is in progress.\n   */\n  isLoading: boolean;\n\n  /**\n   * Abort the current request immediately, keep the current partial object if any.\n   */\n  stop: () => void;\n};\n\nfunction useObject<RESULT, INPUT = any>({\n  api,\n  id,\n  schema, // required, in the future we will use it for validation\n  initialValue,\n  fetch,\n  onError,\n  onFinish,\n  headers,\n  credentials,\n}: Experimental_UseObjectOptions<RESULT>): Experimental_UseObjectHelpers<\n  RESULT,\n  INPUT\n> {\n  // Generate an unique id if not provided.\n  const hookId = useId();\n  const completionId = id ?? hookId;\n\n  // Store the completion state in SWR, using the completionId as the key to share states.\n  const { data, mutate } = useSWR<DeepPartial<RESULT>>(\n    [api, completionId],\n    null,\n    { fallbackData: initialValue },\n  );\n\n  const [error, setError] = useState<undefined | Error>(undefined);\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Abort controller to cancel the current API call.\n  const abortControllerRef = useRef<AbortController | null>(null);\n\n  const stop = useCallback(() => {\n    try {\n      abortControllerRef.current?.abort();\n    } catch (ignored) {\n    } finally {\n      setIsLoading(false);\n      abortControllerRef.current = null;\n    }\n  }, []);\n\n  const submit = async (input: INPUT) => {\n    try {\n      mutate(undefined); // reset the data\n      setIsLoading(true);\n      setError(undefined);\n\n      const abortController = new AbortController();\n      abortControllerRef.current = abortController;\n\n      const actualFetch = fetch ?? getOriginalFetch();\n      const response = await actualFetch(api, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          ...headers,\n        },\n        credentials,\n        signal: abortController.signal,\n        body: JSON.stringify(input),\n      });\n\n      if (!response.ok) {\n        throw new Error(\n          (await response.text()) ?? 'Failed to fetch the response.',\n        );\n      }\n\n      if (response.body == null) {\n        throw new Error('The response body is empty.');\n      }\n\n      let accumulatedText = '';\n      let latestObject: DeepPartial<RESULT> | undefined = undefined;\n\n      await response.body.pipeThrough(new TextDecoderStream()).pipeTo(\n        new WritableStream<string>({\n          write(chunk) {\n            accumulatedText += chunk;\n\n            const { value } = parsePartialJson(accumulatedText);\n            const currentObject = value as DeepPartial<RESULT>;\n\n            if (!isDeepEqualData(latestObject, currentObject)) {\n              latestObject = currentObject;\n\n              mutate(currentObject);\n            }\n          },\n\n          close() {\n            setIsLoading(false);\n            abortControllerRef.current = null;\n\n            if (onFinish != null) {\n              const validationResult = safeValidateTypes({\n                value: latestObject,\n                schema: asSchema(schema),\n              });\n\n              onFinish(\n                validationResult.success\n                  ? { object: validationResult.value, error: undefined }\n                  : { object: undefined, error: validationResult.error },\n              );\n            }\n          },\n        }),\n      );\n    } catch (error) {\n      if (isAbortError(error)) {\n        return;\n      }\n\n      if (onError && error instanceof Error) {\n        onError(error);\n      }\n\n      setIsLoading(false);\n      setError(error instanceof Error ? error : new Error(String(error)));\n    }\n  };\n\n  return {\n    submit,\n    object: data,\n    error,\n    isLoading,\n    stop,\n  };\n}\n\nexport const experimental_useObject = useObject;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,4BAA6B;AAC7B,sBAOO;AACP,mBAA8C;AAG9C,IAAM,mBAAmB,MAAM;AAiFxB,SAAS,aAAa;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAAA;AACF,GAA6C;AAC3C,QAAM,CAAC,UAAU,WAAW,QAAI,uBAAoB,CAAC,CAAC;AACtD,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAAS,EAAE;AACrC,QAAM,CAAC,iBAAiB,kBAAkB,QAAI;AAAA,IAC5C;AAAA,EACF;AACA,QAAM,CAAC,QAAQ,SAAS,QAAI,uBAA0B,kBAAkB;AACxE,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAA4B,MAAS;AAE/D,QAAM,oBAAoB,CACxB,UAGG;AACH,aAAS,MAAM,OAAO,KAAK;AAAA,EAC7B;AAGA,QAAM,yBAAqB,qBAA+B,IAAI;AAE9D,QAAM,WAAO,0BAAY,MAAM;AAC7B,QAAI,mBAAmB,SAAS;AAC9B,yBAAmB,QAAQ,MAAM;AACjC,yBAAmB,UAAU;AAAA,IAC/B;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,QAAM,SAAS,OACb,SACA,mBAGG;AArIP;AAsII,cAAU,aAAa;AAEvB,gBAAY,CAAAC,cAAS;AAxIzB,UAAAC;AAwI4B;AAAA,QACtB,GAAGD;AAAA,QACH;AAAA,UACE,GAAG;AAAA,UACH,KAAIC,MAAA,QAAQ,OAAR,OAAAA,UAAc,4BAAW;AAAA,QAC/B;AAAA,MACF;AAAA,KAAC;AAED,aAAS,EAAE;AAEX,UAAM,kBAAkB,IAAI,gBAAgB;AAE5C,QAAI;AACF,yBAAmB,UAAU;AAE7B,YAAM,cAAcF,UAAA,OAAAA,SAAS,iBAAiB;AAC9C,YAAM,WAAW,MAAM,YAAY,KAAK;AAAA,QACtC,QAAQ;AAAA,QACR;AAAA,QACA,QAAQ,gBAAgB;AAAA,QACxB,SAAS,EAAE,gBAAgB,oBAAoB,GAAG,QAAQ;AAAA,QAC1D,MAAM,KAAK,UAAU;AAAA,UACnB,GAAG;AAAA;AAAA,UAEH,WAAU,6CAAiB,oBAAjB,YAAoC;AAAA,UAC9C,SAAS,QAAQ;AAAA;AAAA,UAGjB,MAAM,iDAAgB;AAAA,QACxB,CAAC;AAAA,MACH,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,IAAI;AAAA,WACP,WAAM,SAAS,KAAK,MAApB,YAA0B;AAAA,QAC7B;AAAA,MACF;AAEA,UAAI,SAAS,QAAQ,MAAM;AACzB,cAAM,IAAI,MAAM,6BAA6B;AAAA,MAC/C;AAEA,gBAAM,wCAAuB;AAAA,QAC3B,QAAQ,SAAS;AAAA,QACjB,uBAAuB,OAAO;AAC5B,sBAAY,CAAAC,cAAY;AAAA,YACtB,GAAGA;AAAA,YACH;AAAA,cACE,IAAI,MAAM;AAAA,cACV,MAAM,MAAM;AAAA,cACZ,SAAS,MAAM,QAAQ,CAAC,EAAE,KAAK;AAAA,cAC/B,OAAO,CAAC;AAAA,YACV;AAAA,UACF,CAAC;AAAA,QACH;AAAA,QACA,WAAW,OAAO;AAEhB,sBAAY,CAAAA,cAAY;AACtB,kBAAM,cAAcA,UAASA,UAAS,SAAS,CAAC;AAChD,mBAAO;AAAA,cACL,GAAGA,UAAS,MAAM,GAAGA,UAAS,SAAS,CAAC;AAAA,cACxC;AAAA,gBACE,IAAI,YAAY;AAAA,gBAChB,MAAM,YAAY;AAAA,gBAClB,SAAS,YAAY,UAAU;AAAA,gBAC/B,OAAO,YAAY;AAAA,cACrB;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAAA,QACA,2BAA2B,OAAO;AAChC,6BAAmB,MAAM,QAAQ;AAGjC,sBAAY,CAAAA,cAAY;AACtB,kBAAM,cAAcA,UAASA,UAAS,SAAS,CAAC;AAChD,wBAAY,KAAK,MAAM;AACvB,mBAAO,CAAC,GAAGA,UAAS,MAAM,GAAGA,UAAS,SAAS,CAAC,GAAG,WAAW;AAAA,UAChE,CAAC;AAAA,QACH;AAAA,QACA,kBAAkB,OAAO;AACvB,sBAAY,CAAAA,cAAS;AAzN/B,gBAAAC;AAyNkC;AAAA,cACtB,GAAGD;AAAA,cACH;AAAA,gBACE,KAAIC,MAAA,MAAM,OAAN,OAAAA,UAAY,4BAAW;AAAA,gBAC3B,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,MAAM,MAAM;AAAA,gBACZ,OAAO,CAAC;AAAA,cACV;AAAA,YACF;AAAA,WAAC;AAAA,QACH;AAAA,QACA,YAAY,OAAO;AACjB,mBAAS,IAAI,MAAM,KAAK,CAAC;AAAA,QAC3B;AAAA,MACF,CAAC;AAAA,IACH,SAASC,QAAO;AAEd,cAAI,oCAAaA,MAAK,KAAK,gBAAgB,OAAO,SAAS;AACzD,2BAAmB,UAAU;AAC7B;AAAA,MACF;AAEA,UAAI,WAAWA,kBAAiB,OAAO;AACrC,gBAAQA,MAAK;AAAA,MACf;AAEA,eAASA,MAAc;AAAA,IACzB,UAAE;AACA,yBAAmB,UAAU;AAC7B,gBAAU,kBAAkB;AAAA,IAC9B;AAAA,EACF;AAEA,QAAM,gBAAgB,OACpB,OACA,mBAGG;AA/PP;AAgQI,yCAAO,mBAAP;AAEA,QAAI,UAAU,IAAI;AAChB;AAAA,IACF;AAEA,WAAO,EAAE,MAAM,QAAQ,SAAS,OAAO,OAAO,CAAC,EAAE,GAAG,cAAc;AAAA,EACpE;AAEA,QAAM,cAAc,CAAC,aAAiC;AACpD,uBAAmB,QAAQ;AAC3B,gBAAY,CAAC,CAAC;AAAA,EAChB;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ACnRA,IAAAC,mBAUO;AACP,IAAAC,gBAAkE;AAClE,iBAAmB;;;ACrBnB,wBAA6B;AAEtB,SAAS,SACd,IACA,QACG;AACH,SAAO,UAAU,WAAO,kBAAAC,SAAiB,IAAI,MAAM,IAAI;AACzD;;;ACPA,IAAAC,mBAAgC;AAChC,IAAAC,gBAAoC;AAM7B,SAAS,eAAkB,aAAmB;AACnD,QAAM,CAAC,OAAO,QAAQ,QAAI,wBAAY,WAAW;AAEjD,+BAAU,MAAM;AACd,QAAI,KAAC,kCAAgB,aAAa,KAAK,GAAG;AACxC,eAAS,WAAW;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,aAAa,KAAK,CAAC;AAEvB,SAAO;AACT;;;AFoGO,SAAS,QAAQ;AAAA,EACtB,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAAC,cAAa,iBAAAC;AAAA,EACb,OAAAC;AAAA,EACA,yBAAyB;AAAA,EACzB,uBAAuB;AACzB,IAkCI,CAAC,GAQH;AAEA,QAAM,CAAC,MAAM,QAAI,wBAASF,WAAU;AAGpC,QAAM,SAAS,kBAAM;AACrB,QAAM,UAAU,OAAO,QAAQ,WAAW,CAAC,KAAK,MAAM,IAAI;AAG1D,QAAM,wBAAwB,eAAe,4CAAmB,CAAC,CAAC;AAClE,QAAM,+BAA2B;AAAA,IAC/B,UAAM,mCAAiB,qBAAqB;AAAA,IAC5C,CAAC,qBAAqB;AAAA,EACxB;AAGA,QAAM,EAAE,MAAM,UAAU,OAAO,QAAI,WAAAG;AAAA,IACjC,CAAC,SAAS,UAAU;AAAA,IACpB;AAAA,IACA,EAAE,cAAc,yBAAyB;AAAA,EAC3C;AAGA,QAAM,kBAAc,sBAAoB,YAAY,CAAC,CAAC;AACtD,+BAAU,MAAM;AACd,gBAAY,UAAU,YAAY,CAAC;AAAA,EACrC,GAAG,CAAC,QAAQ,CAAC;AAGb,QAAM,EAAE,MAAM,YAAY,QAAQ,iBAAiB,QAAI,WAAAA,SAErD,CAAC,SAAS,YAAY,GAAG,IAAI;AAG/B,QAAM,oBAAgB,sBAAgC,UAAU;AAChE,+BAAU,MAAM;AACd,kBAAc,UAAU;AAAA,EAC1B,GAAG,CAAC,UAAU,CAAC;AAEf,QAAM,EAAE,MAAM,SAAS,SAAS,QAAQ,aAAa,QAAI,WAAAA,SAEvD,CAAC,SAAS,QAAQ,GAAG,IAAI;AAE3B,QAAM,EAAE,MAAM,QAAQ,QAAW,QAAQ,SAAS,QAAI,WAAAA,SAEpD,CAAC,SAAS,OAAO,GAAG,IAAI;AAG1B,QAAM,yBAAqB,sBAA+B,IAAI;AAE9D,QAAM,uBAAmB,sBAAO;AAAA,IAC9B;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,+BAAU,MAAM;AACd,qBAAiB,UAAU;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,aAAa,SAAS,IAAI,CAAC;AAE/B,QAAM,qBAAiB;AAAA,IACrB,OACE,aACA,cAAqC,eAClC;AAvPT;AAwPM,mBAAa,WAAW;AACxB,eAAS,MAAS;AAElB,YAAM,mBAAe,mCAAiB,YAAY,QAAQ;AAE1D,YAAM,eAAe,aAAa;AAClC,YAAM,cAAU;AAAA,SACd,kBAAa,aAAa,SAAS,CAAC,MAApC,mBAAuC;AAAA,MACzC;AAEA,UAAI;AACF,cAAM,kBAAkB,IAAI,gBAAgB;AAC5C,2BAAmB,UAAU;AAE7B,cAAM,kBAAkB,SAAS,QAAQ,cAAc;AACvD,cAAM,4BAA4B;AAAA,UAChC;AAAA,UACA;AAAA,QACF;AAGA,cAAM,mBAAmB,YAAY;AACrC,wBAAgB,cAAc,KAAK;AAEnC,cAAM,6BAA6B,yBAC/B,eACA,aAAa;AAAA,UACX,CAAC;AAAA,YACC;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,OAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA,GAAI,6BAA6B,UAAa;AAAA,cAC5C;AAAA,YACF;AAAA,YACA,GAAI,SAAS,UAAa,EAAE,KAAK;AAAA,YACjC,GAAI,gBAAgB,UAAa,EAAE,YAAY;AAAA,YAC/C,GAAI,oBAAoB,UAAa,EAAE,gBAAgB;AAAA,YACvD,GAAI,UAAU,UAAa,EAAE,MAAM;AAAA,UACrC;AAAA,QACF;AAEJ,cAAM,eAAe,cAAc;AAEnC,kBAAM,8BAAY;AAAA,UAChB;AAAA,UACA,OAAM,wFAAkC;AAAA,YACtC,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,aAAa,YAAY;AAAA,YACzB,aAAa,YAAY;AAAA,UAC3B,OALM,YAKA;AAAA,YACJ,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,MAAM,YAAY;AAAA,YAClB,GAAG,iBAAiB,QAAQ;AAAA,YAC5B,GAAG,YAAY;AAAA,UACjB;AAAA,UACA;AAAA,UACA,aAAa,iBAAiB,QAAQ;AAAA,UACtC,SAAS;AAAA,YACP,GAAG,iBAAiB,QAAQ;AAAA,YAC5B,GAAG,YAAY;AAAA,UACjB;AAAA,UACA,iBAAiB,MAAM,mBAAmB;AAAA,UAC1C,2BAA2B;AACzB,gBAAI,CAAC,wBAAwB;AAC3B,8BAAgB,kBAAkB,KAAK;AAAA,YACzC;AAAA,UACF;AAAA,UACA;AAAA,UACA,SAAS,EAAE,SAAS,MAAM,mBAAmB,GAAG;AAC9C,yBAAa,WAAW;AAExB;AAAA,cACE;AAAA,gBACE,GAAI,qBACA,aAAa,MAAM,GAAG,aAAa,SAAS,CAAC,IAC7C;AAAA,gBACJ;AAAA,cACF;AAAA,cACA;AAAA,YACF;AAEA,gBAAI,6BAAM,QAAQ;AAChB;AAAA,gBACE,CAAC,GAAI,sCAAgB,CAAC,GAAI,GAAG,IAAI;AAAA,gBACjC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA,YAAAH;AAAA,UACA,OAAAE;AAAA,UACA,aAAa,aAAa,aAAa,SAAS,CAAC;AAAA,UACjD;AAAA,QACF,CAAC;AAED,2BAAmB,UAAU;AAE7B,qBAAa,OAAO;AAAA,MACtB,SAAS,KAAK;AAEZ,YAAK,IAAY,SAAS,cAAc;AACtC,6BAAmB,UAAU;AAC7B,uBAAa,OAAO;AACpB,iBAAO;AAAA,QACT;AAEA,YAAI,WAAW,eAAe,OAAO;AACnC,kBAAQ,GAAG;AAAA,QACb;AAEA,iBAAS,GAAY;AACrB,qBAAa,OAAO;AAAA,MACtB;AAIA,YAAME,YAAW,YAAY;AAC7B,cACE,yCAAuB;AAAA,QACrB,+BAA+B;AAAA,QAC/B,sBAAsB;AAAA,QACtB;AAAA,QACA,UAAAA;AAAA,MACF,CAAC,GACD;AACA,cAAM,eAAe,EAAE,UAAAA,UAAS,CAAC;AAAA,MACnC;AAAA,IACF;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACAJ;AAAA,MACAE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,QAAM,aAAS;AAAA,IACb,OACE,SACA;AAAA,MACE;AAAA,MACA,SAAAG;AAAA,MACA,MAAAC;AAAA,MACA,2BAA2B,QAAQ;AAAA,IACrC,IAAwB,CAAC,MACtB;AAraT;AAsaM,YAAM,wBAAwB,UAAM;AAAA,QAClC;AAAA,MACF;AAEA,YAAMF,YAAW,YAAY,QAAQ,OAAO;AAAA,QAC1C,GAAG;AAAA,QACH,KAAI,aAAQ,OAAR,YAAcJ,YAAW;AAAA,QAC7B,YAAW,aAAQ,cAAR,YAAqB,oBAAI,KAAK;AAAA,QACzC,0BACE,sBAAsB,SAAS,IAAI,wBAAwB;AAAA,QAC7D,WAAO,kCAAgB,OAAO;AAAA,MAChC,CAAC;AAED,aAAO,eAAe,EAAE,UAAAI,WAAU,SAAAC,UAAS,MAAAC,OAAM,KAAK,CAAC;AAAA,IACzD;AAAA,IACA,CAAC,gBAAgBN,WAAU;AAAA,EAC7B;AAEA,QAAM,aAAS;AAAA,IACb,OAAO,EAAE,MAAM,SAAAK,UAAS,MAAAC,MAAK,IAAwB,CAAC,MAAM;AAC1D,YAAMF,YAAW,YAAY;AAE7B,UAAIA,UAAS,WAAW,GAAG;AACzB,eAAO;AAAA,MACT;AAGA,YAAM,cAAcA,UAASA,UAAS,SAAS,CAAC;AAChD,aAAO,eAAe;AAAA,QACpB,UACE,YAAY,SAAS,cAAcA,UAAS,MAAM,GAAG,EAAE,IAAIA;AAAA,QAC7D,SAAAC;AAAA,QACA,MAAAC;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,cAAc;AAAA,EACjB;AAEA,QAAM,WAAO,2BAAY,MAAM;AAC7B,QAAI,mBAAmB,SAAS;AAC9B,yBAAmB,QAAQ,MAAM;AACjC,yBAAmB,UAAU;AAAA,IAC/B;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,QAAM,0BAAsB,2BAAY,YAAY;AAClD,UAAMF,YAAW,YAAY;AAE7B,mBAAe,EAAE,UAAAA,UAAS,GAAG,QAAQ;AAAA,EACvC,GAAG,CAAC,cAAc,CAAC;AAEnB,QAAM,kBAAc;AAAA,IAClB,CAACA,cAA+D;AAC9D,UAAI,OAAOA,cAAa,YAAY;AAClC,QAAAA,YAAWA,UAAS,YAAY,OAAO;AAAA,MACzC;AAEA,YAAM,wBAAoB,mCAAiBA,SAAQ;AACnD,aAAO,mBAAmB,KAAK;AAC/B,kBAAY,UAAU;AAAA,IACxB;AAAA,IACA,CAAC,MAAM;AAAA,EACT;AAEA,QAAM,cAAU;AAAA,IACd,CACE,SAIG;AACH,UAAI,OAAO,SAAS,YAAY;AAC9B,eAAO,KAAK,cAAc,OAAO;AAAA,MACnC;AAEA,uBAAiB,MAAM,KAAK;AAC5B,oBAAc,UAAU;AAAA,IAC1B;AAAA,IACA,CAAC,gBAAgB;AAAA,EACnB;AAGA,QAAM,CAAC,OAAO,QAAQ,QAAI,wBAAS,YAAY;AAE/C,QAAM,mBAAe;AAAA,IACnB,OACE,OACA,UAA8B,CAAC,GAC/B,aACG;AAhgBT;AAigBM,2CAAO,mBAAP;AAEA,UAAI,CAAC,SAAS,CAAC,QAAQ;AAAkB;AAEzC,UAAI,UAAU;AACZ,yBAAiB,UAAU;AAAA,UACzB,GAAG,iBAAiB;AAAA,UACpB,GAAG;AAAA,QACL;AAAA,MACF;AAEA,YAAM,wBAAwB,UAAM;AAAA,QAClC,QAAQ;AAAA,MACV;AAEA,YAAMA,YAAW,YAAY,QAAQ,OAAO;AAAA,QAC1C,IAAIJ,YAAW;AAAA,QACf,WAAW,oBAAI,KAAK;AAAA,QACpB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,0BACE,sBAAsB,SAAS,IAAI,wBAAwB;AAAA,QAC7D,OAAO,CAAC,EAAE,MAAM,QAAQ,MAAM,MAAM,CAAC;AAAA,MACvC,CAAC;AAED,YAAM,cAA2B;AAAA,QAC/B,UAAAI;AAAA,QACA,SAAS,QAAQ;AAAA,QACjB,MAAM,QAAQ;AAAA,QACd,MAAM,QAAQ;AAAA,MAChB;AAEA,qBAAe,WAAW;AAE1B,eAAS,EAAE;AAAA,IACb;AAAA,IACA,CAAC,OAAOJ,aAAY,cAAc;AAAA,EACpC;AAEA,QAAM,oBAAoB,CAAC,MAAW;AACpC,aAAS,EAAE,OAAO,KAAK;AAAA,EACzB;AAEA,QAAM,oBAAgB;AAAA,IACpB,CAAC,EAAE,YAAY,OAAO,MAA+C;AACnE,YAAM,kBAAkB,YAAY;AAEpC,iDAAqB;AAAA,QACnB,UAAU;AAAA,QACV;AAAA,QACA,YAAY;AAAA,MACd,CAAC;AAGD;AAAA,QACE;AAAA,UACE,GAAG,gBAAgB,MAAM,GAAG,gBAAgB,SAAS,CAAC;AAAA,UACtD,EAAE,GAAG,gBAAgB,gBAAgB,SAAS,CAAC,EAAE;AAAA,QACnD;AAAA,QACA;AAAA,MACF;AAGA,UAAI,WAAW,eAAe,WAAW,aAAa;AACpD;AAAA,MACF;AAGA,YAAM,cAAc,gBAAgB,gBAAgB,SAAS,CAAC;AAC9D,cAAI,2DAAyC,WAAW,GAAG;AACzD,uBAAe,EAAE,UAAU,gBAAgB,CAAC;AAAA,MAC9C;AAAA,IACF;AAAA,IACA,CAAC,QAAQ,QAAQ,cAAc;AAAA,EACjC;AAEA,SAAO;AAAA,IACL,UAAU,8BAAY,CAAC;AAAA,IACvB,IAAI;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW,WAAW,eAAe,WAAW;AAAA,IAChD;AAAA,IACA;AAAA,EACF;AACF;;;AGhmBA,IAAAO,mBAKO;AACP,IAAAC,gBAAgE;AAChE,IAAAC,cAAmB;AA2DZ,SAAS,cAAc;AAAA,EAC5B,MAAM;AAAA,EACN;AAAA,EACA,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA,iBAAiB;AAAA,EACjB,OAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AACzB,IAMI,CAAC,GAAyB;AAE5B,QAAM,aAAS,qBAAM;AACrB,QAAM,eAAe,MAAM;AAG3B,QAAM,EAAE,MAAM,OAAO,QAAI,YAAAC,SAAe,CAAC,KAAK,YAAY,GAAG,MAAM;AAAA,IACjE,cAAc;AAAA,EAChB,CAAC;AAED,QAAM,EAAE,MAAM,YAAY,OAAO,QAAQ,cAAc,QAAI,YAAAA;AAAA,IACzD,CAAC,cAAc,SAAS;AAAA,IACxB;AAAA,EACF;AAEA,QAAM,EAAE,MAAM,YAAY,QAAQ,iBAAiB,QAAI,YAAAA,SAErD,CAAC,cAAc,YAAY,GAAG,IAAI;AAEpC,QAAM,CAAC,OAAO,QAAQ,QAAI,wBAA4B,MAAS;AAC/D,QAAM,aAAa;AAGnB,QAAM,CAAC,iBAAiB,kBAAkB,QACxC,wBAAiC,IAAI;AAEvC,QAAM,uBAAmB,sBAAO;AAAA,IAC9B;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,+BAAU,MAAM;AACd,qBAAiB,UAAU;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,aAAa,SAAS,IAAI,CAAC;AAE/B,QAAM,qBAAiB;AAAA,IACrB,OAAO,QAAgB,gBACrB,oCAAkB;AAAA,MAChB;AAAA,MACA;AAAA,MACA,aAAa,iBAAiB,QAAQ;AAAA,MACtC,SAAS,EAAE,GAAG,iBAAiB,QAAQ,SAAS,GAAG,mCAAS,QAAQ;AAAA,MACpE,MAAM;AAAA,QACJ,GAAG,iBAAiB,QAAQ;AAAA,QAC5B,GAAG,mCAAS;AAAA,MACd;AAAA,MACA;AAAA,MACA,OAAAD;AAAA;AAAA,MAEA,eAAe;AAAA,QACb,CAACE,gBAAuB,OAAOA,aAAY,KAAK;AAAA,QAChD;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,CAACC,UACC,iBAAiB,CAAC,GAAI,kCAAc,CAAC,GAAI,GAAIA,SAAA,OAAAA,QAAQ,CAAC,CAAE,GAAG,KAAK;AAAA,QAClE;AAAA,MACF;AAAA,MACA,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IACH;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACAH;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,QAAM,WAAO,2BAAY,MAAM;AAC7B,QAAI,iBAAiB;AACnB,sBAAgB,MAAM;AACtB,yBAAmB,IAAI;AAAA,IACzB;AAAA,EACF,GAAG,CAAC,eAAe,CAAC;AAEpB,QAAM,oBAAgB;AAAA,IACpB,CAACE,gBAAuB;AACtB,aAAOA,aAAY,KAAK;AAAA,IAC1B;AAAA,IACA,CAAC,MAAM;AAAA,EACT;AAEA,QAAM,eAAW;AAAA,IACf,OAAO,QAAQ,YAAY;AACzB,aAAO,eAAe,QAAQ,OAAO;AAAA,IACvC;AAAA,IACA,CAAC,cAAc;AAAA,EACjB;AAEA,QAAM,CAAC,OAAO,QAAQ,QAAI,wBAAS,YAAY;AAE/C,QAAM,mBAAe;AAAA,IACnB,CAAC,UAA4C;AAtMjD;AAuMM,2CAAO,mBAAP;AACA,aAAO,QAAQ,SAAS,KAAK,IAAI;AAAA,IACnC;AAAA,IACA,CAAC,OAAO,QAAQ;AAAA,EAClB;AAEA,QAAM,wBAAoB;AAAA,IACxB,CAAC,MAAW;AACV,eAAS,EAAE,OAAO,KAAK;AAAA,IACzB;AAAA,IACA,CAAC,QAAQ;AAAA,EACX;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR;AACF;;;ACjOA,IAAAE,yBAIO;AACP,IAAAC,mBAMO;AACP,IAAAC,gBAAqD;AACrD,IAAAC,cAAmB;AAInB,IAAMC,oBAAmB,MAAM;AA4F/B,SAAS,UAA+B;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EACA;AAAA,EACA,OAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAGE;AAEA,QAAM,aAAS,qBAAM;AACrB,QAAM,eAAe,kBAAM;AAG3B,QAAM,EAAE,MAAM,OAAO,QAAI,YAAAC;AAAA,IACvB,CAAC,KAAK,YAAY;AAAA,IAClB;AAAA,IACA,EAAE,cAAc,aAAa;AAAA,EAC/B;AAEA,QAAM,CAAC,OAAO,QAAQ,QAAI,wBAA4B,MAAS;AAC/D,QAAM,CAAC,WAAW,YAAY,QAAI,wBAAS,KAAK;AAGhD,QAAM,yBAAqB,sBAA+B,IAAI;AAE9D,QAAM,WAAO,2BAAY,MAAM;AA5IjC;AA6II,QAAI;AACF,+BAAmB,YAAnB,mBAA4B;AAAA,IAC9B,SAAS,SAAS;AAAA,IAClB,UAAE;AACA,mBAAa,KAAK;AAClB,yBAAmB,UAAU;AAAA,IAC/B;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,QAAM,SAAS,OAAO,UAAiB;AAtJzC;AAuJI,QAAI;AACF,aAAO,MAAS;AAChB,mBAAa,IAAI;AACjB,eAAS,MAAS;AAElB,YAAM,kBAAkB,IAAI,gBAAgB;AAC5C,yBAAmB,UAAU;AAE7B,YAAM,cAAcD,UAAA,OAAAA,SAASD,kBAAiB;AAC9C,YAAM,WAAW,MAAM,YAAY,KAAK;AAAA,QACtC,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,UAChB,GAAG;AAAA,QACL;AAAA,QACA;AAAA,QACA,QAAQ,gBAAgB;AAAA,QACxB,MAAM,KAAK,UAAU,KAAK;AAAA,MAC5B,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,IAAI;AAAA,WACP,WAAM,SAAS,KAAK,MAApB,YAA0B;AAAA,QAC7B;AAAA,MACF;AAEA,UAAI,SAAS,QAAQ,MAAM;AACzB,cAAM,IAAI,MAAM,6BAA6B;AAAA,MAC/C;AAEA,UAAI,kBAAkB;AACtB,UAAI,eAAgD;AAEpD,YAAM,SAAS,KAAK,YAAY,IAAI,kBAAkB,CAAC,EAAE;AAAA,QACvD,IAAI,eAAuB;AAAA,UACzB,MAAM,OAAO;AACX,+BAAmB;AAEnB,kBAAM,EAAE,MAAM,QAAI,mCAAiB,eAAe;AAClD,kBAAM,gBAAgB;AAEtB,gBAAI,KAAC,kCAAgB,cAAc,aAAa,GAAG;AACjD,6BAAe;AAEf,qBAAO,aAAa;AAAA,YACtB;AAAA,UACF;AAAA,UAEA,QAAQ;AACN,yBAAa,KAAK;AAClB,+BAAmB,UAAU;AAE7B,gBAAI,YAAY,MAAM;AACpB,oBAAM,uBAAmB,0CAAkB;AAAA,gBACzC,OAAO;AAAA,gBACP,YAAQ,2BAAS,MAAM;AAAA,cACzB,CAAC;AAED;AAAA,gBACE,iBAAiB,UACb,EAAE,QAAQ,iBAAiB,OAAO,OAAO,OAAU,IACnD,EAAE,QAAQ,QAAW,OAAO,iBAAiB,MAAM;AAAA,cACzD;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,SAASG,QAAO;AACd,cAAI,qCAAaA,MAAK,GAAG;AACvB;AAAA,MACF;AAEA,UAAI,WAAWA,kBAAiB,OAAO;AACrC,gBAAQA,MAAK;AAAA,MACf;AAEA,mBAAa,KAAK;AAClB,eAASA,kBAAiB,QAAQA,SAAQ,IAAI,MAAM,OAAOA,MAAK,CAAC,CAAC;AAAA,IACpE;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,IAAM,yBAAyB;", "names": ["fetch", "messages", "_a", "error", "import_ui_utils", "import_react", "throttleFunction", "import_ui_utils", "import_react", "generateId", "generateIdFunc", "fetch", "useSWR", "messages", "headers", "body", "import_ui_utils", "import_react", "import_swr", "fetch", "useSWR", "completion", "data", "import_provider_utils", "import_ui_utils", "import_react", "import_swr", "getOriginalFetch", "fetch", "useSWR", "error"]}
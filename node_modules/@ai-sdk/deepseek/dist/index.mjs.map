{"version": 3, "sources": ["../src/deepseek-provider.ts", "../src/deepseek-metadata-extractor.ts"], "sourcesContent": ["import { OpenAICompatibleChatLanguageModel } from '@ai-sdk/openai-compatible';\nimport {\n  LanguageModelV1,\n  NoSuchModelError,\n  ProviderV1,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  loadApiKey,\n  withoutTrailingSlash,\n} from '@ai-sdk/provider-utils';\nimport {\n  DeepSeekChatModelId,\n  DeepSeekChatSettings,\n} from './deepseek-chat-settings';\nimport { deepSeekMetadataExtractor } from './deepseek-metadata-extractor';\n\nexport interface DeepSeekProviderSettings {\n  /**\nDeepSeek API key.\n*/\n  apiKey?: string;\n  /**\nBase URL for the API calls.\n*/\n  baseURL?: string;\n  /**\nCustom headers to include in the requests.\n*/\n  headers?: Record<string, string>;\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n*/\n  fetch?: FetchFunction;\n}\n\nexport interface DeepSeekProvider extends ProviderV1 {\n  /**\nCreates a DeepSeek model for text generation.\n*/\n  (\n    modelId: DeepSeekChatModelId,\n    settings?: DeepSeekChatSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates a DeepSeek model for text generation.\n*/\n  languageModel(\n    modelId: DeepSeekChatModelId,\n    settings?: DeepSeekChatSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates a DeepSeek chat model for text generation.\n*/\n  chat(\n    modelId: DeepSeekChatModelId,\n    settings?: DeepSeekChatSettings,\n  ): LanguageModelV1;\n}\n\nexport function createDeepSeek(\n  options: DeepSeekProviderSettings = {},\n): DeepSeekProvider {\n  const baseURL = withoutTrailingSlash(\n    options.baseURL ?? 'https://api.deepseek.com/v1',\n  );\n  const getHeaders = () => ({\n    Authorization: `Bearer ${loadApiKey({\n      apiKey: options.apiKey,\n      environmentVariableName: 'DEEPSEEK_API_KEY',\n      description: 'DeepSeek API key',\n    })}`,\n    ...options.headers,\n  });\n\n  const createLanguageModel = (\n    modelId: DeepSeekChatModelId,\n    settings: DeepSeekChatSettings = {},\n  ) => {\n    return new OpenAICompatibleChatLanguageModel(modelId, settings, {\n      provider: `deepseek.chat`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch,\n      defaultObjectGenerationMode: 'json',\n      metadataExtractor: deepSeekMetadataExtractor,\n    });\n  };\n\n  const provider = (\n    modelId: DeepSeekChatModelId,\n    settings?: DeepSeekChatSettings,\n  ) => createLanguageModel(modelId, settings);\n\n  provider.languageModel = createLanguageModel;\n  provider.chat = createLanguageModel;\n  provider.textEmbeddingModel = (modelId: string) => {\n    throw new NoSuchModelError({ modelId, modelType: 'textEmbeddingModel' });\n  };\n\n  return provider;\n}\n\nexport const deepseek = createDeepSeek();\n", "import { MetadataExtractor } from '@ai-sdk/openai-compatible';\nimport { safeValidateTypes } from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\n\nconst buildDeepseekMetadata = (\n  usage: z.infer<typeof deepSeekUsageSchema> | undefined,\n) => {\n  return usage == null\n    ? undefined\n    : {\n        deepseek: {\n          promptCacheHitTokens: usage.prompt_cache_hit_tokens ?? NaN,\n          promptCacheMissTokens: usage.prompt_cache_miss_tokens ?? NaN,\n        },\n      };\n};\n\nexport const deepSeekMetadataExtractor: MetadataExtractor = {\n  extractMetadata: ({ parsedBody }: { parsedBody: unknown }) => {\n    const parsed = safeValidateTypes({\n      value: parsedBody,\n      schema: deepSeekResponseSchema,\n    });\n\n    return !parsed.success || parsed.value.usage == null\n      ? undefined\n      : buildDeepseekMetadata(parsed.value.usage);\n  },\n\n  createStreamExtractor: () => {\n    let usage: z.infer<typeof deepSeekUsageSchema> | undefined;\n\n    return {\n      processChunk: (chunk: unknown) => {\n        const parsed = safeValidateTypes({\n          value: chunk,\n          schema: deepSeekStreamChunkSchema,\n        });\n\n        if (\n          parsed.success &&\n          parsed.value.choices?.[0]?.finish_reason === 'stop' &&\n          parsed.value.usage\n        ) {\n          usage = parsed.value.usage;\n        }\n      },\n      buildMetadata: () => buildDeepseekMetadata(usage),\n    };\n  },\n};\n\nconst deepSeekUsageSchema = z.object({\n  prompt_cache_hit_tokens: z.number().nullish(),\n  prompt_cache_miss_tokens: z.number().nullish(),\n});\n\nconst deepSeekResponseSchema = z.object({\n  usage: deepSeekUsageSchema.nullish(),\n});\n\nconst deepSeekStreamChunkSchema = z.object({\n  choices: z\n    .array(\n      z.object({\n        finish_reason: z.string().nullish(),\n      }),\n    )\n    .nullish(),\n  usage: deepSeekUsageSchema.nullish(),\n});\n"], "mappings": ";AAAA,SAAS,yCAAyC;AAClD;AAAA,EAEE;AAAA,OAEK;AACP;AAAA,EAEE;AAAA,EACA;AAAA,OACK;;;ACTP,SAAS,yBAAyB;AAClC,SAAS,SAAS;AAElB,IAAM,wBAAwB,CAC5B,UACG;AANL;AAOE,SAAO,SAAS,OACZ,SACA;AAAA,IACE,UAAU;AAAA,MACR,uBAAsB,WAAM,4BAAN,YAAiC;AAAA,MACvD,wBAAuB,WAAM,6BAAN,YAAkC;AAAA,IAC3D;AAAA,EACF;AACN;AAEO,IAAM,4BAA+C;AAAA,EAC1D,iBAAiB,CAAC,EAAE,WAAW,MAA+B;AAC5D,UAAM,SAAS,kBAAkB;AAAA,MAC/B,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AAED,WAAO,CAAC,OAAO,WAAW,OAAO,MAAM,SAAS,OAC5C,SACA,sBAAsB,OAAO,MAAM,KAAK;AAAA,EAC9C;AAAA,EAEA,uBAAuB,MAAM;AAC3B,QAAI;AAEJ,WAAO;AAAA,MACL,cAAc,CAAC,UAAmB;AAjCxC;AAkCQ,cAAM,SAAS,kBAAkB;AAAA,UAC/B,OAAO;AAAA,UACP,QAAQ;AAAA,QACV,CAAC;AAED,YACE,OAAO,aACP,kBAAO,MAAM,YAAb,mBAAuB,OAAvB,mBAA2B,mBAAkB,UAC7C,OAAO,MAAM,OACb;AACA,kBAAQ,OAAO,MAAM;AAAA,QACvB;AAAA,MACF;AAAA,MACA,eAAe,MAAM,sBAAsB,KAAK;AAAA,IAClD;AAAA,EACF;AACF;AAEA,IAAM,sBAAsB,EAAE,OAAO;AAAA,EACnC,yBAAyB,EAAE,OAAO,EAAE,QAAQ;AAAA,EAC5C,0BAA0B,EAAE,OAAO,EAAE,QAAQ;AAC/C,CAAC;AAED,IAAM,yBAAyB,EAAE,OAAO;AAAA,EACtC,OAAO,oBAAoB,QAAQ;AACrC,CAAC;AAED,IAAM,4BAA4B,EAAE,OAAO;AAAA,EACzC,SAAS,EACN;AAAA,IACC,EAAE,OAAO;AAAA,MACP,eAAe,EAAE,OAAO,EAAE,QAAQ;AAAA,IACpC,CAAC;AAAA,EACH,EACC,QAAQ;AAAA,EACX,OAAO,oBAAoB,QAAQ;AACrC,CAAC;;;ADPM,SAAS,eACd,UAAoC,CAAC,GACnB;AAjEpB;AAkEE,QAAM,UAAU;AAAA,KACd,aAAQ,YAAR,YAAmB;AAAA,EACrB;AACA,QAAM,aAAa,OAAO;AAAA,IACxB,eAAe,UAAU,WAAW;AAAA,MAClC,QAAQ,QAAQ;AAAA,MAChB,yBAAyB;AAAA,MACzB,aAAa;AAAA,IACf,CAAC,CAAC;AAAA,IACF,GAAG,QAAQ;AAAA,EACb;AAEA,QAAM,sBAAsB,CAC1B,SACA,WAAiC,CAAC,MAC/B;AACH,WAAO,IAAI,kCAAkC,SAAS,UAAU;AAAA,MAC9D,UAAU;AAAA,MACV,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,OAAO,GAAG,IAAI;AAAA,MACpC,SAAS;AAAA,MACT,OAAO,QAAQ;AAAA,MACf,6BAA6B;AAAA,MAC7B,mBAAmB;AAAA,IACrB,CAAC;AAAA,EACH;AAEA,QAAM,WAAW,CACf,SACA,aACG,oBAAoB,SAAS,QAAQ;AAE1C,WAAS,gBAAgB;AACzB,WAAS,OAAO;AAChB,WAAS,qBAAqB,CAAC,YAAoB;AACjD,UAAM,IAAI,iBAAiB,EAAE,SAAS,WAAW,qBAAqB,CAAC;AAAA,EACzE;AAEA,SAAO;AACT;AAEO,IAAM,WAAW,eAAe;", "names": []}
{"name": "@ai-sdk/openai-compatible", "version": "0.2.14", "license": "Apache-2.0", "sideEffects": false, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**/*", "internal/dist/**/*", "CHANGELOG.md"], "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./internal": {"types": "./internal/dist/index.d.ts", "import": "./internal/dist/index.mjs", "module": "./internal/dist/index.mjs", "require": "./internal/dist/index.js"}}, "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.8"}, "devDependencies": {"@types/node": "20.17.24", "tsup": "^8", "typescript": "5.6.3", "zod": "3.23.8", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "engines": {"node": ">=18"}, "publishConfig": {"access": "public"}, "homepage": "https://ai-sdk.dev/docs", "repository": {"type": "git", "url": "git+https://github.com/vercel/ai.git"}, "bugs": {"url": "https://github.com/vercel/ai/issues"}, "keywords": ["ai"], "scripts": {"build": "tsup", "build:watch": "tsup --watch", "clean": "rm -rf dist && rm -rf internal/dist", "lint": "eslint \"./**/*.ts*\"", "type-check": "tsc --noEmit", "prettier-check": "prettier --check \"./**/*.ts*\"", "test": "pnpm test:node && pnpm test:edge", "test:edge": "vitest --config vitest.edge.config.js --run", "test:node": "vitest --config vitest.node.config.js --run", "test:node:watch": "vitest --config vitest.node.config.js "}}
{"version": 3, "sources": ["../../src/internal/index.ts", "../../src/convert-to-openai-compatible-chat-messages.ts", "../../src/map-openai-compatible-finish-reason.ts", "../../src/get-response-metadata.ts"], "sourcesContent": ["export { convertToOpenAICompatibleChatMessages } from '../convert-to-openai-compatible-chat-messages';\nexport { mapOpenAICompatibleFinishReason } from '../map-openai-compatible-finish-reason';\nexport { getResponseMetadata } from '../get-response-metadata';\nexport type { OpenAICompatibleChatConfig } from '../openai-compatible-chat-language-model';\n", "import {\n  LanguageModelV1Prompt,\n  LanguageModelV1ProviderMetadata,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { convertUint8ArrayToBase64 } from '@ai-sdk/provider-utils';\nimport { OpenAICompatibleChatPrompt } from './openai-compatible-api-types';\n\nfunction getOpenAIMetadata(message: {\n  providerMetadata?: LanguageModelV1ProviderMetadata;\n}) {\n  return message?.providerMetadata?.openaiCompatible ?? {};\n}\n\nexport function convertToOpenAICompatibleChatMessages(\n  prompt: LanguageModelV1Prompt,\n): OpenAICompatibleChatPrompt {\n  const messages: OpenAICompatibleChatPrompt = [];\n  for (const { role, content, ...message } of prompt) {\n    const metadata = getOpenAIMetadata({ ...message });\n    switch (role) {\n      case 'system': {\n        messages.push({ role: 'system', content, ...metadata });\n        break;\n      }\n\n      case 'user': {\n        if (content.length === 1 && content[0].type === 'text') {\n          messages.push({\n            role: 'user',\n            content: content[0].text,\n            ...getOpenAIMetadata(content[0]),\n          });\n          break;\n        }\n\n        messages.push({\n          role: 'user',\n          content: content.map(part => {\n            const partMetadata = getOpenAIMetadata(part);\n            switch (part.type) {\n              case 'text': {\n                return { type: 'text', text: part.text, ...partMetadata };\n              }\n              case 'image': {\n                return {\n                  type: 'image_url',\n                  image_url: {\n                    url:\n                      part.image instanceof URL\n                        ? part.image.toString()\n                        : `data:${\n                            part.mimeType ?? 'image/jpeg'\n                          };base64,${convertUint8ArrayToBase64(part.image)}`,\n                  },\n                  ...partMetadata,\n                };\n              }\n              case 'file': {\n                throw new UnsupportedFunctionalityError({\n                  functionality: 'File content parts in user messages',\n                });\n              }\n            }\n          }),\n          ...metadata,\n        });\n\n        break;\n      }\n\n      case 'assistant': {\n        let text = '';\n        const toolCalls: Array<{\n          id: string;\n          type: 'function';\n          function: { name: string; arguments: string };\n        }> = [];\n\n        for (const part of content) {\n          const partMetadata = getOpenAIMetadata(part);\n          switch (part.type) {\n            case 'text': {\n              text += part.text;\n              break;\n            }\n            case 'tool-call': {\n              toolCalls.push({\n                id: part.toolCallId,\n                type: 'function',\n                function: {\n                  name: part.toolName,\n                  arguments: JSON.stringify(part.args),\n                },\n                ...partMetadata,\n              });\n              break;\n            }\n          }\n        }\n\n        messages.push({\n          role: 'assistant',\n          content: text,\n          tool_calls: toolCalls.length > 0 ? toolCalls : undefined,\n          ...metadata,\n        });\n\n        break;\n      }\n\n      case 'tool': {\n        for (const toolResponse of content) {\n          const toolResponseMetadata = getOpenAIMetadata(toolResponse);\n          messages.push({\n            role: 'tool',\n            tool_call_id: toolResponse.toolCallId,\n            content: JSON.stringify(toolResponse.result),\n            ...toolResponseMetadata,\n          });\n        }\n        break;\n      }\n\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return messages;\n}\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\n\nexport function mapOpenAICompatibleFinishReason(\n  finishReason: string | null | undefined,\n): LanguageModelV1FinishReason {\n  switch (finishReason) {\n    case 'stop':\n      return 'stop';\n    case 'length':\n      return 'length';\n    case 'content_filter':\n      return 'content-filter';\n    case 'function_call':\n    case 'tool_calls':\n      return 'tool-calls';\n    default:\n      return 'unknown';\n  }\n}\n", "export function getResponseMetadata({\n  id,\n  model,\n  created,\n}: {\n  id?: string | undefined | null;\n  created?: number | undefined | null;\n  model?: string | undefined | null;\n}) {\n  return {\n    id: id ?? undefined,\n    modelId: model ?? undefined,\n    timestamp: created != null ? new Date(created * 1000) : undefined,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,sBAIO;AACP,4BAA0C;AAG1C,SAAS,kBAAkB,SAExB;AAVH;AAWE,UAAO,8CAAS,qBAAT,mBAA2B,qBAA3B,YAA+C,CAAC;AACzD;AAEO,SAAS,sCACd,QAC4B;AAC5B,QAAM,WAAuC,CAAC;AAC9C,aAAW,EAAE,MAAM,SAAS,GAAG,QAAQ,KAAK,QAAQ;AAClD,UAAM,WAAW,kBAAkB,EAAE,GAAG,QAAQ,CAAC;AACjD,YAAQ,MAAM;AAAA,MACZ,KAAK,UAAU;AACb,iBAAS,KAAK,EAAE,MAAM,UAAU,SAAS,GAAG,SAAS,CAAC;AACtD;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AACX,YAAI,QAAQ,WAAW,KAAK,QAAQ,CAAC,EAAE,SAAS,QAAQ;AACtD,mBAAS,KAAK;AAAA,YACZ,MAAM;AAAA,YACN,SAAS,QAAQ,CAAC,EAAE;AAAA,YACpB,GAAG,kBAAkB,QAAQ,CAAC,CAAC;AAAA,UACjC,CAAC;AACD;AAAA,QACF;AAEA,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,SAAS,QAAQ,IAAI,UAAQ;AAtCvC;AAuCY,kBAAM,eAAe,kBAAkB,IAAI;AAC3C,oBAAQ,KAAK,MAAM;AAAA,cACjB,KAAK,QAAQ;AACX,uBAAO,EAAE,MAAM,QAAQ,MAAM,KAAK,MAAM,GAAG,aAAa;AAAA,cAC1D;AAAA,cACA,KAAK,SAAS;AACZ,uBAAO;AAAA,kBACL,MAAM;AAAA,kBACN,WAAW;AAAA,oBACT,KACE,KAAK,iBAAiB,MAClB,KAAK,MAAM,SAAS,IACpB,SACE,UAAK,aAAL,YAAiB,YACnB,eAAW,iDAA0B,KAAK,KAAK,CAAC;AAAA,kBACxD;AAAA,kBACA,GAAG;AAAA,gBACL;AAAA,cACF;AAAA,cACA,KAAK,QAAQ;AACX,sBAAM,IAAI,8CAA8B;AAAA,kBACtC,eAAe;AAAA,gBACjB,CAAC;AAAA,cACH;AAAA,YACF;AAAA,UACF,CAAC;AAAA,UACD,GAAG;AAAA,QACL,CAAC;AAED;AAAA,MACF;AAAA,MAEA,KAAK,aAAa;AAChB,YAAI,OAAO;AACX,cAAM,YAID,CAAC;AAEN,mBAAW,QAAQ,SAAS;AAC1B,gBAAM,eAAe,kBAAkB,IAAI;AAC3C,kBAAQ,KAAK,MAAM;AAAA,YACjB,KAAK,QAAQ;AACX,sBAAQ,KAAK;AACb;AAAA,YACF;AAAA,YACA,KAAK,aAAa;AAChB,wBAAU,KAAK;AAAA,gBACb,IAAI,KAAK;AAAA,gBACT,MAAM;AAAA,gBACN,UAAU;AAAA,kBACR,MAAM,KAAK;AAAA,kBACX,WAAW,KAAK,UAAU,KAAK,IAAI;AAAA,gBACrC;AAAA,gBACA,GAAG;AAAA,cACL,CAAC;AACD;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,SAAS;AAAA,UACT,YAAY,UAAU,SAAS,IAAI,YAAY;AAAA,UAC/C,GAAG;AAAA,QACL,CAAC;AAED;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AACX,mBAAW,gBAAgB,SAAS;AAClC,gBAAM,uBAAuB,kBAAkB,YAAY;AAC3D,mBAAS,KAAK;AAAA,YACZ,MAAM;AAAA,YACN,cAAc,aAAa;AAAA,YAC3B,SAAS,KAAK,UAAU,aAAa,MAAM;AAAA,YAC3C,GAAG;AAAA,UACL,CAAC;AAAA,QACH;AACA;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;AClIO,SAAS,gCACd,cAC6B;AAC7B,UAAQ,cAAc;AAAA,IACpB,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;;;AClBO,SAAS,oBAAoB;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AACF,GAIG;AACD,SAAO;AAAA,IACL,IAAI,kBAAM;AAAA,IACV,SAAS,wBAAS;AAAA,IAClB,WAAW,WAAW,OAAO,IAAI,KAAK,UAAU,GAAI,IAAI;AAAA,EAC1D;AACF;", "names": []}
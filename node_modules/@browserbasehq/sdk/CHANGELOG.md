# Changelog

## 2.6.0 (2025-05-16)

Full Changelog: [v2.5.0...v2.6.0](https://github.com/browserbase/sdk-node/compare/v2.5.0...v2.6.0)

### Features

* **api:** api update ([0b43bac](https://github.com/browserbase/sdk-node/commit/0b43baccf8dbb13ed4b9bb8feed15849007cc531))
* **api:** api update ([#158](https://github.com/browserbase/sdk-node/issues/158)) ([1287216](https://github.com/browserbase/sdk-node/commit/12872160a6e7be94d82cb4189cf53dc9eecbfbba))


### Bug Fixes

* **api:** improve type resolution when importing as a package ([#163](https://github.com/browserbase/sdk-node/issues/163)) ([758c8e0](https://github.com/browserbase/sdk-node/commit/758c8e048537bdc6d0070a11010ed307b5d4de03))
* **client:** send `X-Stainless-Timeout` in seconds ([#161](https://github.com/browserbase/sdk-node/issues/161)) ([8aea1ee](https://github.com/browserbase/sdk-node/commit/8aea1ee6f66052fa976087c6dbb57f98385c6eee))
* **mcp:** remove unused tools.ts ([#164](https://github.com/browserbase/sdk-node/issues/164)) ([b09bd54](https://github.com/browserbase/sdk-node/commit/b09bd54af116847e852f67bf6c7f0e32dfa92509))


### Chores

* **ci:** add timeout thresholds for CI jobs ([c813b38](https://github.com/browserbase/sdk-node/commit/c813b38b3be8470e2c5b95b58c621ccd7b7ccfe5))
* **ci:** only use depot for staging repos ([475fe50](https://github.com/browserbase/sdk-node/commit/475fe5018be88d96971697bdc3e1b33ae9669310))
* **client:** minor internal fixes ([ee36919](https://github.com/browserbase/sdk-node/commit/ee369190b385d25726cac1e3bc0e23559539d8d9))
* **internal:** add aliases for Record and Array ([#162](https://github.com/browserbase/sdk-node/issues/162)) ([e658ed9](https://github.com/browserbase/sdk-node/commit/e658ed9e739c0479ffd542ffe4b616fc5884d77d))
* **internal:** codegen related update ([99b8089](https://github.com/browserbase/sdk-node/commit/99b8089de31eb102cb82a9b85b2d13fd57570f5c))
* **internal:** reduce CI branch coverage ([37d8e14](https://github.com/browserbase/sdk-node/commit/37d8e1406cc38d5e2003f40b43a7632c7f528544))
* **internal:** upload builds and expand CI branch coverage ([d30a38a](https://github.com/browserbase/sdk-node/commit/d30a38a6331169eb4ef7672ae6fbd561cd5c5b0c))


### Documentation

* **readme:** fix typo ([7bc4b9b](https://github.com/browserbase/sdk-node/commit/7bc4b9ba006fef118f7883ed2fab7cc140906f97))

## 2.5.0 (2025-03-28)

Full Changelog: [v2.4.0...v2.5.0](https://github.com/browserbase/sdk-node/compare/v2.4.0...v2.5.0)

### Features

* **api:** api update ([#151](https://github.com/browserbase/sdk-node/issues/151)) ([9433265](https://github.com/browserbase/sdk-node/commit/9433265b54e3b560152be7642a5a314c82d8ad08))
* **api:** api update ([#153](https://github.com/browserbase/sdk-node/issues/153)) ([53c928c](https://github.com/browserbase/sdk-node/commit/53c928cbc008d2b8b33d405ecd853a46e960ffd5))


### Bug Fixes

* avoid type error in certain environments ([#154](https://github.com/browserbase/sdk-node/issues/154)) ([0288fa5](https://github.com/browserbase/sdk-node/commit/0288fa562ed1397cfba2cc7c77ae060e3e747382))
* **internal:** work around https://github.com/vercel/next.js/issues/76881 ([#155](https://github.com/browserbase/sdk-node/issues/155)) ([f2b25bc](https://github.com/browserbase/sdk-node/commit/f2b25bc31f4dd4d5a1978065478cca1dfd75526d))


### Chores

* **exports:** cleaner resource index imports ([#149](https://github.com/browserbase/sdk-node/issues/149)) ([ef04d35](https://github.com/browserbase/sdk-node/commit/ef04d358436d2883af62c88281dad0a379900512))
* **exports:** stop using path fallbacks ([#150](https://github.com/browserbase/sdk-node/issues/150)) ([32c5ee7](https://github.com/browserbase/sdk-node/commit/32c5ee7d6de48ac826b608e4e582e37fa92a5188))
* **internal:** version bump ([#147](https://github.com/browserbase/sdk-node/issues/147)) ([ea90300](https://github.com/browserbase/sdk-node/commit/ea9030070914ad0d790dd89a81a266cc54434161))

## 2.4.0 (2025-03-14)

Full Changelog: [v2.3.0...v2.4.0](https://github.com/browserbase/sdk-node/compare/v2.3.0...v2.4.0)

### Features

* **api:** api update ([#142](https://github.com/browserbase/sdk-node/issues/142)) ([b2391f8](https://github.com/browserbase/sdk-node/commit/b2391f8eb6b6a35d033de3b54a8731a59601c177))
* **api:** api update ([#144](https://github.com/browserbase/sdk-node/issues/144)) ([c7aaea1](https://github.com/browserbase/sdk-node/commit/c7aaea13fae457c258a8ee97b7b1717866f15b2d))


### Bug Fixes

* **client:** fix export map for index exports ([#137](https://github.com/browserbase/sdk-node/issues/137)) ([7e3ba08](https://github.com/browserbase/sdk-node/commit/7e3ba0874d40256119ab5f9f556bff5a43425188))
* **exports:** ensure resource imports don't require /index ([#146](https://github.com/browserbase/sdk-node/issues/146)) ([c6ec9a5](https://github.com/browserbase/sdk-node/commit/c6ec9a5a8a87c6e1d811945c89bc674188198bf7))


### Chores

* **internal:** codegen related update ([#138](https://github.com/browserbase/sdk-node/issues/138)) ([316a6af](https://github.com/browserbase/sdk-node/commit/316a6af67a7c7daa6d78956e7dab1fd88f4bf90c))
* **internal:** codegen related update ([#143](https://github.com/browserbase/sdk-node/issues/143)) ([d8d3fb9](https://github.com/browserbase/sdk-node/commit/d8d3fb9f1d3ee6497cd781fee475e475416237cf))
* **internal:** fix devcontainers setup ([#139](https://github.com/browserbase/sdk-node/issues/139)) ([40202bd](https://github.com/browserbase/sdk-node/commit/40202bdf590f7df08d872c70a362ac7c3c1393a2))
* **internal:** version bump ([#129](https://github.com/browserbase/sdk-node/issues/129)) ([82c57f9](https://github.com/browserbase/sdk-node/commit/82c57f9b9c51dd5e29c4ccdb1855a8883b28ab08))
* **internal:** version bump ([#135](https://github.com/browserbase/sdk-node/issues/135)) ([90a4906](https://github.com/browserbase/sdk-node/commit/90a4906a5fbddf73b49cd45293619a9deadcb3e0))


### Documentation

* update URLs from stainlessapi.com to stainless.com ([#140](https://github.com/browserbase/sdk-node/issues/140)) ([0e240d7](https://github.com/browserbase/sdk-node/commit/0e240d7076fba9e8220d0fadb177426fe7bf3972))

## 2.3.0 (2025-02-05)

Full Changelog: [v2.2.0...v2.3.0](https://github.com/browserbase/sdk-node/compare/v2.2.0...v2.3.0)

### Features

* **client:** send `X-Stainless-Timeout` header ([#133](https://github.com/browserbase/sdk-node/issues/133)) ([1b03cd8](https://github.com/browserbase/sdk-node/commit/1b03cd8b5e8a3830915686c429d7188f8c5e1f2f))


### Chores

* **internal:** version bump ([#129](https://github.com/browserbase/sdk-node/issues/129)) ([579f065](https://github.com/browserbase/sdk-node/commit/579f065310a7d06b13b9c6a82adf78718fcd3751))

## 2.2.0 (2025-01-28)

Full Changelog: [v2.1.3...v2.2.0](https://github.com/browserbase/sdk-node/compare/v2.1.3...v2.2.0)

### Features

* **api:** api update ([#120](https://github.com/browserbase/sdk-node/issues/120)) ([b3315e0](https://github.com/browserbase/sdk-node/commit/b3315e0d03cd446d04f792d9879a862eaf4f7457))
* **api:** api update ([#123](https://github.com/browserbase/sdk-node/issues/123)) ([5eeaf0f](https://github.com/browserbase/sdk-node/commit/5eeaf0f4e859801eb4facf5cdf21cd4aeaa62088))
* **api:** api update ([#125](https://github.com/browserbase/sdk-node/issues/125)) ([fa43f83](https://github.com/browserbase/sdk-node/commit/fa43f8345b5980ca505284d707661834c4a63f4d))
* **api:** api update ([#127](https://github.com/browserbase/sdk-node/issues/127)) ([6ef46ba](https://github.com/browserbase/sdk-node/commit/6ef46ba28bc54a55934ff589e8c428d9a55853b0))
* **api:** api update ([#128](https://github.com/browserbase/sdk-node/issues/128)) ([57f01ee](https://github.com/browserbase/sdk-node/commit/57f01ee25e486de815c54faefbbd258b304e0b86))


### Bug Fixes

* **client:** normalize method ([#114](https://github.com/browserbase/sdk-node/issues/114)) ([d1d66f4](https://github.com/browserbase/sdk-node/commit/d1d66f41bc3240c2cc103e982bead4593984bac8))
* **client:** normalize method ([#85](https://github.com/browserbase/sdk-node/issues/85)) ([4e2fd4f](https://github.com/browserbase/sdk-node/commit/4e2fd4f97d9199bcd71fa6284523b87c8aff098f))


### Chores

* **internal:** codegen related update ([#100](https://github.com/browserbase/sdk-node/issues/100)) ([be0fb87](https://github.com/browserbase/sdk-node/commit/be0fb8729648a039633abf9f4f820e1f1371fab1))
* **internal:** codegen related update ([#101](https://github.com/browserbase/sdk-node/issues/101)) ([cdc0098](https://github.com/browserbase/sdk-node/commit/cdc0098f0fe3e346a6c39a49d146df9718283253))
* **internal:** codegen related update ([#102](https://github.com/browserbase/sdk-node/issues/102)) ([a205eb3](https://github.com/browserbase/sdk-node/commit/a205eb32749b3de8f155b7095e539f5ecf312288))
* **internal:** codegen related update ([#103](https://github.com/browserbase/sdk-node/issues/103)) ([8bbb29d](https://github.com/browserbase/sdk-node/commit/8bbb29daf468178558017018bdda99680ffa7192))
* **internal:** codegen related update ([#104](https://github.com/browserbase/sdk-node/issues/104)) ([aa09378](https://github.com/browserbase/sdk-node/commit/aa093789041fbe3d4ddbc518ae1720492df7b1b4))
* **internal:** codegen related update ([#105](https://github.com/browserbase/sdk-node/issues/105)) ([f7decdd](https://github.com/browserbase/sdk-node/commit/f7decddf22a6d00e2f07d2249366b2e20ff4d36d))
* **internal:** codegen related update ([#106](https://github.com/browserbase/sdk-node/issues/106)) ([5f6cf39](https://github.com/browserbase/sdk-node/commit/5f6cf39442ce44cccd8eefb1c590fee5dce14b3b))
* **internal:** codegen related update ([#107](https://github.com/browserbase/sdk-node/issues/107)) ([65569d9](https://github.com/browserbase/sdk-node/commit/65569d9c6281cc6cb4c52e463047c72728c946e8))
* **internal:** codegen related update ([#108](https://github.com/browserbase/sdk-node/issues/108)) ([2efdd29](https://github.com/browserbase/sdk-node/commit/2efdd2931bde64ac04c1ead6577a687a06681ca6))
* **internal:** codegen related update ([#109](https://github.com/browserbase/sdk-node/issues/109)) ([ee7a567](https://github.com/browserbase/sdk-node/commit/ee7a567fe5589ac6efd1ba01644d0f241f753d4a))
* **internal:** codegen related update ([#110](https://github.com/browserbase/sdk-node/issues/110)) ([147d29c](https://github.com/browserbase/sdk-node/commit/147d29cd2ab04a7a6112d816346dc63a7370fec2))
* **internal:** codegen related update ([#111](https://github.com/browserbase/sdk-node/issues/111)) ([b506c94](https://github.com/browserbase/sdk-node/commit/b506c940c4a6c609837e4f50986275646e730d90))
* **internal:** codegen related update ([#112](https://github.com/browserbase/sdk-node/issues/112)) ([c726f9d](https://github.com/browserbase/sdk-node/commit/c726f9d17683e467c06cdbb2de79618177386c19))
* **internal:** codegen related update ([#113](https://github.com/browserbase/sdk-node/issues/113)) ([00d3797](https://github.com/browserbase/sdk-node/commit/00d3797ddd1a43bb0881ab43efb83efcd649f8b6))
* **internal:** codegen related update ([#116](https://github.com/browserbase/sdk-node/issues/116)) ([efc7bbf](https://github.com/browserbase/sdk-node/commit/efc7bbf3920fa50b8f51ba32f6a83b04e1ac97ea))
* **internal:** codegen related update ([#117](https://github.com/browserbase/sdk-node/issues/117)) ([03b5237](https://github.com/browserbase/sdk-node/commit/03b5237346188285df228c2f49d46e22b7ee5786))
* **internal:** codegen related update ([#122](https://github.com/browserbase/sdk-node/issues/122)) ([58f702b](https://github.com/browserbase/sdk-node/commit/58f702b63d349f2728b917fda106efb66572857a))
* **internal:** codegen related update ([#126](https://github.com/browserbase/sdk-node/issues/126)) ([3535bba](https://github.com/browserbase/sdk-node/commit/3535bbabe8ef40b0239b9552b1edac8331eee317))
* **internal:** codegen related update ([#50](https://github.com/browserbase/sdk-node/issues/50)) ([e25fad2](https://github.com/browserbase/sdk-node/commit/e25fad2a55873f45deeb15bc7820e632f5aae3c5))
* **internal:** codegen related update ([#74](https://github.com/browserbase/sdk-node/issues/74)) ([ac7c39c](https://github.com/browserbase/sdk-node/commit/ac7c39c82bc6f9814cc259b7d8774c222ae70fb5))
* **internal:** codegen related update ([#75](https://github.com/browserbase/sdk-node/issues/75)) ([e515faa](https://github.com/browserbase/sdk-node/commit/e515faa3f6d4c6731bec6bc318ba052a02ea2efd))
* **internal:** codegen related update ([#76](https://github.com/browserbase/sdk-node/issues/76)) ([b0ba40b](https://github.com/browserbase/sdk-node/commit/b0ba40b4c439e75bb755e6e8d103a6c1a913bad6))
* **internal:** codegen related update ([#77](https://github.com/browserbase/sdk-node/issues/77)) ([5844462](https://github.com/browserbase/sdk-node/commit/58444621fe47d62d4ec42e7f217e87c3399dab67))
* **internal:** codegen related update ([#78](https://github.com/browserbase/sdk-node/issues/78)) ([ba95d78](https://github.com/browserbase/sdk-node/commit/ba95d78eb013d9e7ed2faba452d5dc0e3fb756ae))
* **internal:** codegen related update ([#79](https://github.com/browserbase/sdk-node/issues/79)) ([b938f98](https://github.com/browserbase/sdk-node/commit/b938f98baffe628484d6a3053c386c3e1dbfe4d5))
* **internal:** codegen related update ([#80](https://github.com/browserbase/sdk-node/issues/80)) ([e6c215a](https://github.com/browserbase/sdk-node/commit/e6c215a1ba0ad7daa91c6f520cf7d64f0f4b6c7f))
* **internal:** codegen related update ([#81](https://github.com/browserbase/sdk-node/issues/81)) ([aa5fc7e](https://github.com/browserbase/sdk-node/commit/aa5fc7ecbb097e5a6dfafea5e7a9fb64232326a1))
* **internal:** codegen related update ([#83](https://github.com/browserbase/sdk-node/issues/83)) ([7b7d276](https://github.com/browserbase/sdk-node/commit/7b7d276cce847a084d2682be06c92ee3a58f80dd))
* **internal:** codegen related update ([#84](https://github.com/browserbase/sdk-node/issues/84)) ([ee770ed](https://github.com/browserbase/sdk-node/commit/ee770ed39d3277f03c73144b56811eadc25efe54))
* **internal:** codegen related update ([#86](https://github.com/browserbase/sdk-node/issues/86)) ([838f955](https://github.com/browserbase/sdk-node/commit/838f9551bf71c4274a591a8efa15a423f326a6b5))
* **internal:** codegen related update ([#87](https://github.com/browserbase/sdk-node/issues/87)) ([95a1664](https://github.com/browserbase/sdk-node/commit/95a16646e3396982aeaa966ca0db290709fe9132))
* **internal:** codegen related update ([#88](https://github.com/browserbase/sdk-node/issues/88)) ([aed6281](https://github.com/browserbase/sdk-node/commit/aed6281d8c51c865e326335bff8280ae1d6e184b))
* **internal:** codegen related update ([#89](https://github.com/browserbase/sdk-node/issues/89)) ([351f322](https://github.com/browserbase/sdk-node/commit/351f322384882660e62bede689ad71d5eb4d0eb8))
* **internal:** codegen related update ([#90](https://github.com/browserbase/sdk-node/issues/90)) ([98fd97d](https://github.com/browserbase/sdk-node/commit/98fd97d3040a15b531744ed37304e8636a222f66))
* **internal:** codegen related update ([#92](https://github.com/browserbase/sdk-node/issues/92)) ([f47ac33](https://github.com/browserbase/sdk-node/commit/f47ac3381405e0d95b8719314f64abe07219497a))
* **internal:** codegen related update ([#93](https://github.com/browserbase/sdk-node/issues/93)) ([d928cb6](https://github.com/browserbase/sdk-node/commit/d928cb64e9f2e9ac250b221041ff46f0c70d7004))
* **internal:** codegen related update ([#94](https://github.com/browserbase/sdk-node/issues/94)) ([2a1800b](https://github.com/browserbase/sdk-node/commit/2a1800b04f7cf1a2750bf53dbf9a123b76574b69))
* **internal:** codegen related update ([#95](https://github.com/browserbase/sdk-node/issues/95)) ([869bc76](https://github.com/browserbase/sdk-node/commit/869bc76f86bdb092d2dae762ba8ac32604ee2148))
* **internal:** codegen related update ([#96](https://github.com/browserbase/sdk-node/issues/96)) ([6395c53](https://github.com/browserbase/sdk-node/commit/6395c538eb71082dee21b3066cf8f9cd99ea7f31))
* **internal:** codegen related update ([#97](https://github.com/browserbase/sdk-node/issues/97)) ([e112f24](https://github.com/browserbase/sdk-node/commit/e112f2455725494257254d73f41e51c5358f80f0))
* **internal:** codegen related update ([#98](https://github.com/browserbase/sdk-node/issues/98)) ([65ae889](https://github.com/browserbase/sdk-node/commit/65ae889d6759e8ede4d5df568809c189589d4d6a))
* **internal:** codegen related update ([#99](https://github.com/browserbase/sdk-node/issues/99)) ([72be8df](https://github.com/browserbase/sdk-node/commit/72be8dfd796d2d4965b901473bd1cb1e1b71acf4))
* **internal:** version bump ([#56](https://github.com/browserbase/sdk-node/issues/56)) ([ddaf4f3](https://github.com/browserbase/sdk-node/commit/ddaf4f3c3fdadcdf0cfd6c998fdd7d811b4d1286))
* **internal:** version bump ([#60](https://github.com/browserbase/sdk-node/issues/60)) ([0f373a9](https://github.com/browserbase/sdk-node/commit/0f373a9bac3674b5e33708ab9866096a25366e8f))
* **internal:** version bump ([#70](https://github.com/browserbase/sdk-node/issues/70)) ([366b99b](https://github.com/browserbase/sdk-node/commit/366b99bbcf8acc3bf0ec9abae2b7b8f944656280))


### Documentation

* minor formatting changes ([#115](https://github.com/browserbase/sdk-node/issues/115)) ([815478e](https://github.com/browserbase/sdk-node/commit/815478e0443d9405263bdd49cc300c19aca10452))
* minor formatting changes ([#82](https://github.com/browserbase/sdk-node/issues/82)) ([f2f77f8](https://github.com/browserbase/sdk-node/commit/f2f77f8a47b3422d422b9eaf1d8ddbaebb817031))
* minor formatting changes ([#91](https://github.com/browserbase/sdk-node/issues/91)) ([16cda6f](https://github.com/browserbase/sdk-node/commit/16cda6fe2ca0e68ceed0f34d354355e85d8441fb))

## 2.1.3 (2025-01-10)

Full Changelog: [v2.1.2...v2.1.3](https://github.com/browserbase/sdk-node/compare/v2.1.2...v2.1.3)

### Bug Fixes

* **client:** normalize method ([#114](https://github.com/browserbase/sdk-node/issues/114)) ([cffc41f](https://github.com/browserbase/sdk-node/commit/cffc41f7baae3bc4fa18eaaa345daaf97077f69b))
* **client:** normalize method ([#73](https://github.com/browserbase/sdk-node/issues/73)) ([d18d217](https://github.com/browserbase/sdk-node/commit/d18d217242c61280c58c2ea63868a5e854d87c54))
* **client:** normalize method ([#85](https://github.com/browserbase/sdk-node/issues/85)) ([4a0a13a](https://github.com/browserbase/sdk-node/commit/4a0a13a486b1b19d86ef8fea3977a6f18e266401))


### Chores

* **internal:** codegen related update ([#100](https://github.com/browserbase/sdk-node/issues/100)) ([01c6b28](https://github.com/browserbase/sdk-node/commit/01c6b28a6c345865d0789523bcccae1bd4990eb8))
* **internal:** codegen related update ([#101](https://github.com/browserbase/sdk-node/issues/101)) ([241e0fd](https://github.com/browserbase/sdk-node/commit/241e0fdd3fb85cc60d206ff40220741b636eaf29))
* **internal:** codegen related update ([#102](https://github.com/browserbase/sdk-node/issues/102)) ([adbf0ac](https://github.com/browserbase/sdk-node/commit/adbf0acd4341416a540d4c81f0536f964e33db6b))
* **internal:** codegen related update ([#103](https://github.com/browserbase/sdk-node/issues/103)) ([a04e944](https://github.com/browserbase/sdk-node/commit/a04e9444a5cc4940418e82b81a0dd6bc68614497))
* **internal:** codegen related update ([#104](https://github.com/browserbase/sdk-node/issues/104)) ([2d639f9](https://github.com/browserbase/sdk-node/commit/2d639f936d5b4f8dad5d1a300701ac27f4efe702))
* **internal:** codegen related update ([#105](https://github.com/browserbase/sdk-node/issues/105)) ([0d61709](https://github.com/browserbase/sdk-node/commit/0d61709a981e4d56e6b5b4693dcc4a87fa87a4c6))
* **internal:** codegen related update ([#106](https://github.com/browserbase/sdk-node/issues/106)) ([3d19415](https://github.com/browserbase/sdk-node/commit/3d194156db3f8a1c98c933367a1d7aa48ad41e5d))
* **internal:** codegen related update ([#107](https://github.com/browserbase/sdk-node/issues/107)) ([f21383a](https://github.com/browserbase/sdk-node/commit/f21383a99ce073953deac289f9d94e4d59308e6f))
* **internal:** codegen related update ([#108](https://github.com/browserbase/sdk-node/issues/108)) ([47b9c65](https://github.com/browserbase/sdk-node/commit/47b9c65e487011900f1996e4f35ceb867a5f5045))
* **internal:** codegen related update ([#109](https://github.com/browserbase/sdk-node/issues/109)) ([63a2c0d](https://github.com/browserbase/sdk-node/commit/63a2c0df0a552721d77b59e8f1ac1a0540cab7d3))
* **internal:** codegen related update ([#110](https://github.com/browserbase/sdk-node/issues/110)) ([d17c241](https://github.com/browserbase/sdk-node/commit/d17c241b712cc5325ed8de918906890ecd1ab856))
* **internal:** codegen related update ([#111](https://github.com/browserbase/sdk-node/issues/111)) ([85ae78d](https://github.com/browserbase/sdk-node/commit/85ae78d8ebd917b7a37a871c803122786889761e))
* **internal:** codegen related update ([#112](https://github.com/browserbase/sdk-node/issues/112)) ([ef21c4a](https://github.com/browserbase/sdk-node/commit/ef21c4a382a4b4485c68504ca7db396ee221df1a))
* **internal:** codegen related update ([#113](https://github.com/browserbase/sdk-node/issues/113)) ([bb69228](https://github.com/browserbase/sdk-node/commit/bb6922817abf6f6167f411e13961d346435549b4))
* **internal:** codegen related update ([#50](https://github.com/browserbase/sdk-node/issues/50)) ([bb9da1d](https://github.com/browserbase/sdk-node/commit/bb9da1dcc8336d0e33fc8dd40519ea8db1738223))
* **internal:** codegen related update ([#72](https://github.com/browserbase/sdk-node/issues/72)) ([415c0c3](https://github.com/browserbase/sdk-node/commit/415c0c3ba38437c2b4235fdb689f096cc062345e))
* **internal:** codegen related update ([#74](https://github.com/browserbase/sdk-node/issues/74)) ([4e54df4](https://github.com/browserbase/sdk-node/commit/4e54df4bf0147ca39f66a39cd213336db457a415))
* **internal:** codegen related update ([#75](https://github.com/browserbase/sdk-node/issues/75)) ([d344d5e](https://github.com/browserbase/sdk-node/commit/d344d5e923d0aa58ea8c7a1de5d16e49e24b8a26))
* **internal:** codegen related update ([#76](https://github.com/browserbase/sdk-node/issues/76)) ([2233aa2](https://github.com/browserbase/sdk-node/commit/2233aa277ba33ede0ef04c2df2ae89b204f2251e))
* **internal:** codegen related update ([#77](https://github.com/browserbase/sdk-node/issues/77)) ([16b27b4](https://github.com/browserbase/sdk-node/commit/16b27b41e10e2ca536488ee5b642609a18f54ba3))
* **internal:** codegen related update ([#78](https://github.com/browserbase/sdk-node/issues/78)) ([af2d145](https://github.com/browserbase/sdk-node/commit/af2d14584d893bc5fadb35fea91522fb384f093d))
* **internal:** codegen related update ([#79](https://github.com/browserbase/sdk-node/issues/79)) ([b7e4a89](https://github.com/browserbase/sdk-node/commit/b7e4a89a5ac5cc81c1a1e4d3383fe3618336e169))
* **internal:** codegen related update ([#80](https://github.com/browserbase/sdk-node/issues/80)) ([85a35bf](https://github.com/browserbase/sdk-node/commit/85a35bfd6b35fd8dec0e0337c3a3d7ba5c2de2dc))
* **internal:** codegen related update ([#81](https://github.com/browserbase/sdk-node/issues/81)) ([2b037d2](https://github.com/browserbase/sdk-node/commit/2b037d292891181f8e8184318c0e9e66ef3205ab))
* **internal:** codegen related update ([#83](https://github.com/browserbase/sdk-node/issues/83)) ([b1feede](https://github.com/browserbase/sdk-node/commit/b1feedee3363c361a9494344db4f70bdfb7bfc45))
* **internal:** codegen related update ([#84](https://github.com/browserbase/sdk-node/issues/84)) ([eaf66e0](https://github.com/browserbase/sdk-node/commit/eaf66e050f77f83b8c49ff48929298c185ccd2da))
* **internal:** codegen related update ([#86](https://github.com/browserbase/sdk-node/issues/86)) ([f9b16d1](https://github.com/browserbase/sdk-node/commit/f9b16d1ebfcf83a6bfabd962629dad5b842a6d75))
* **internal:** codegen related update ([#87](https://github.com/browserbase/sdk-node/issues/87)) ([62821e0](https://github.com/browserbase/sdk-node/commit/62821e0562b2911918e7b4bd2a6829e56cda77fd))
* **internal:** codegen related update ([#88](https://github.com/browserbase/sdk-node/issues/88)) ([9ca705d](https://github.com/browserbase/sdk-node/commit/9ca705d9eb8f30d3c2f3b2fb949784ada1bd5432))
* **internal:** codegen related update ([#89](https://github.com/browserbase/sdk-node/issues/89)) ([2c9ed02](https://github.com/browserbase/sdk-node/commit/2c9ed025f4f456ade76bff1f9431ad8ccb8ba643))
* **internal:** codegen related update ([#90](https://github.com/browserbase/sdk-node/issues/90)) ([e6e5e10](https://github.com/browserbase/sdk-node/commit/e6e5e1022c331a8a9f8c347dff329831a108316f))
* **internal:** codegen related update ([#92](https://github.com/browserbase/sdk-node/issues/92)) ([603179e](https://github.com/browserbase/sdk-node/commit/603179e15ff192b48a849a891c37d33daa44a978))
* **internal:** codegen related update ([#93](https://github.com/browserbase/sdk-node/issues/93)) ([8e16851](https://github.com/browserbase/sdk-node/commit/8e1685118774d324812441d22dda22e5490fbdf9))
* **internal:** codegen related update ([#94](https://github.com/browserbase/sdk-node/issues/94)) ([763fff0](https://github.com/browserbase/sdk-node/commit/763fff0013483ef8f0dd451a67396043b0a9c5e7))
* **internal:** codegen related update ([#95](https://github.com/browserbase/sdk-node/issues/95)) ([3e6a5b4](https://github.com/browserbase/sdk-node/commit/3e6a5b430b60ba3e4f70ca84f77286f6d30a455a))
* **internal:** codegen related update ([#96](https://github.com/browserbase/sdk-node/issues/96)) ([925575c](https://github.com/browserbase/sdk-node/commit/925575c15dcee8bcb8e84182e858bc188ed64163))
* **internal:** codegen related update ([#97](https://github.com/browserbase/sdk-node/issues/97)) ([66c0b51](https://github.com/browserbase/sdk-node/commit/66c0b517f16827b4787c8770f2dca5a81c514873))
* **internal:** codegen related update ([#98](https://github.com/browserbase/sdk-node/issues/98)) ([1533ff8](https://github.com/browserbase/sdk-node/commit/1533ff8b958470fc99c6c15a7c04c918f428916e))
* **internal:** codegen related update ([#99](https://github.com/browserbase/sdk-node/issues/99)) ([668a9d1](https://github.com/browserbase/sdk-node/commit/668a9d105602b36c3cb6295cb6491fd9ffe20c80))
* **internal:** version bump ([#56](https://github.com/browserbase/sdk-node/issues/56)) ([afafa41](https://github.com/browserbase/sdk-node/commit/afafa41deebd9c03518a8a57c4492d612f3f73ee))
* **internal:** version bump ([#60](https://github.com/browserbase/sdk-node/issues/60)) ([1734b28](https://github.com/browserbase/sdk-node/commit/1734b28c1d1f2e13bf368feb7f910d060aa646fd))
* **internal:** version bump ([#70](https://github.com/browserbase/sdk-node/issues/70)) ([40771ce](https://github.com/browserbase/sdk-node/commit/40771ceb4bbf4cee6fc98b8fef4f39cd096fdecd))


### Documentation

* minor formatting changes ([#115](https://github.com/browserbase/sdk-node/issues/115)) ([6a71bb3](https://github.com/browserbase/sdk-node/commit/6a71bb30e5206d2d9c77e605a712185eecee7f34))
* minor formatting changes ([#82](https://github.com/browserbase/sdk-node/issues/82)) ([414b9b0](https://github.com/browserbase/sdk-node/commit/414b9b0efb0e3b3e1db5a2bd9250fbce85569f2c))
* minor formatting changes ([#91](https://github.com/browserbase/sdk-node/issues/91)) ([baa864c](https://github.com/browserbase/sdk-node/commit/baa864cb2b3c8da5fffe2f3634bb293f4f0afbac))

## 2.1.2 (2024-12-29)

Full Changelog: [v2.1.1...v2.1.2](https://github.com/browserbase/sdk-node/compare/v2.1.1...v2.1.2)

### Chores

* **internal:** codegen related update ([#50](https://github.com/browserbase/sdk-node/issues/50)) ([fa3fa06](https://github.com/browserbase/sdk-node/commit/fa3fa06b0c5b44e60dccd45a7d262341d7d4431c))
* **internal:** version bump ([#56](https://github.com/browserbase/sdk-node/issues/56)) ([fc0e9aa](https://github.com/browserbase/sdk-node/commit/fc0e9aa25f78e3136ceab807eb2df592b7a83e46))
* **internal:** version bump ([#60](https://github.com/browserbase/sdk-node/issues/60)) ([702abb2](https://github.com/browserbase/sdk-node/commit/702abb2c4e7d85100ea110528e70850d4a4f2c52))
* **internal:** version bump ([#67](https://github.com/browserbase/sdk-node/issues/67)) ([93995d5](https://github.com/browserbase/sdk-node/commit/93995d5e6b9d833a428ae0501d6d4700c3a2d67d))

## 2.1.1 (2024-12-12)

Full Changelog: [v2.1.0...v2.1.1](https://github.com/browserbase/sdk-node/compare/v2.1.0...v2.1.1)

### Chores

* **internal:** bump cross-spawn to v7.0.6 ([#63](https://github.com/browserbase/sdk-node/issues/63)) ([43a8303](https://github.com/browserbase/sdk-node/commit/43a83039ad2c853410f4918e637597f637edaac0))
* **internal:** codegen related update ([#50](https://github.com/browserbase/sdk-node/issues/50)) ([bb1dfba](https://github.com/browserbase/sdk-node/commit/bb1dfba218c17fc31ec1c5a51d7d0cc85fe1e81a))
* **internal:** codegen related update ([#65](https://github.com/browserbase/sdk-node/issues/65)) ([03b26a2](https://github.com/browserbase/sdk-node/commit/03b26a2b9205acf68ad254ec8be20d65e9212ef3))
* **internal:** remove unnecessary getRequestClient function ([#62](https://github.com/browserbase/sdk-node/issues/62)) ([ad17e82](https://github.com/browserbase/sdk-node/commit/ad17e821be22d01ee1521fd5d1fdb6a3a95865e2))
* **internal:** version bump ([#56](https://github.com/browserbase/sdk-node/issues/56)) ([1434357](https://github.com/browserbase/sdk-node/commit/1434357ce1efc7895b717c18593461a6d3ea8dc0))
* **internal:** version bump ([#60](https://github.com/browserbase/sdk-node/issues/60)) ([535d652](https://github.com/browserbase/sdk-node/commit/535d652bb074cd9a7a37ee647e670c716fca3b1b))
* **types:** nicer error class types + jsdocs ([#64](https://github.com/browserbase/sdk-node/issues/64)) ([7bf7d7b](https://github.com/browserbase/sdk-node/commit/7bf7d7b7058accc456b51928495b8b21b0ace08c))

## 2.1.0 (2024-11-29)

Full Changelog: [v2.0.2...v2.1.0](https://github.com/browserbase/sdk-node/compare/v2.0.2...v2.1.0)

### Features

* **internal:** make git install file structure match npm ([#58](https://github.com/browserbase/sdk-node/issues/58)) ([f7ea01e](https://github.com/browserbase/sdk-node/commit/f7ea01e8f1b5261dc0fb3db028ff3fc1bea004ed))


### Chores

* **internal:** codegen related update ([#50](https://github.com/browserbase/sdk-node/issues/50)) ([101931a](https://github.com/browserbase/sdk-node/commit/101931aed3555b911b5e96ba6cc7cab33f22c163))
* **internal:** version bump ([#56](https://github.com/browserbase/sdk-node/issues/56)) ([0f0b97a](https://github.com/browserbase/sdk-node/commit/0f0b97aab53c566075dadb8c91bd4ff1014dd54b))

## 2.0.2 (2024-11-20)

Full Changelog: [v2.0.1...v2.0.2](https://github.com/browserbase/sdk-node/compare/v2.0.1...v2.0.2)

### Chores

* **internal:** codegen related update ([#50](https://github.com/browserbase/sdk-node/issues/50)) ([3ae78b0](https://github.com/browserbase/sdk-node/commit/3ae78b0477cb19382fb37f66c36c8dfdd5c2c4ec))
* remove redundant word in comment ([#53](https://github.com/browserbase/sdk-node/issues/53)) ([93f75da](https://github.com/browserbase/sdk-node/commit/93f75da8176b3756133682ef622bd359f011832d))


### Documentation

* remove suggestion to use `npm` call out ([#52](https://github.com/browserbase/sdk-node/issues/52)) ([41832d5](https://github.com/browserbase/sdk-node/commit/41832d5220e500837950cfc565a6d40d03ff695a))

## 2.0.1 (2024-11-19)

Full Changelog: [v2.0.0...v2.0.1](https://github.com/browserbase/sdk-node/compare/v2.0.0...v2.0.1)

### Features

* **api:** api update ([#40](https://github.com/browserbase/sdk-node/issues/40)) ([97dddaa](https://github.com/browserbase/sdk-node/commit/97dddaa2729f64c4d1dbf20bebf7c243e3fb723b))
* **api:** api update ([#42](https://github.com/browserbase/sdk-node/issues/42)) ([2ad71d8](https://github.com/browserbase/sdk-node/commit/2ad71d8e96157370ec64619568d185805dcc97a2))
* various codegen changes ([7615211](https://github.com/browserbase/sdk-node/commit/7615211d48d953fe0833d35a3986f24673659840))


### Bug Fixes

* update example ([#38](https://github.com/browserbase/sdk-node/issues/38)) ([242167d](https://github.com/browserbase/sdk-node/commit/242167d2a82a6239fda100ad014436c9ee77a1de))


### Chores

* rebuild project due to codegen change ([#44](https://github.com/browserbase/sdk-node/issues/44)) ([804a9ec](https://github.com/browserbase/sdk-node/commit/804a9ec93ae35b48c6c7d89948a4146abf5ab8cc))
* rebuild project due to codegen change ([#45](https://github.com/browserbase/sdk-node/issues/45)) ([889bd8d](https://github.com/browserbase/sdk-node/commit/889bd8d94a245a1f9e26dc6877b045fc5bcbae0a))
* rebuild project due to codegen change ([#47](https://github.com/browserbase/sdk-node/issues/47)) ([4d08a25](https://github.com/browserbase/sdk-node/commit/4d08a25596cf991be1576d10fa9801cb057b157b))
* rebuild project due to codegen change ([#48](https://github.com/browserbase/sdk-node/issues/48)) ([189b64f](https://github.com/browserbase/sdk-node/commit/189b64fca8fe9ee76ff0b888108cd276f0387ad3))

## 2.0.0 (2024-10-29)

🆕 fully-featured SDK for Browserbase's API

"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Recording = void 0;
const resource_1 = require("../../resource.js");
class Recording extends resource_1.APIResource {
    /**
     * Session Recording
     */
    retrieve(id, options) {
        return this._client.get(`/v1/sessions/${id}/recording`, options);
    }
}
exports.Recording = Recording;
//# sourceMappingURL=recording.js.map
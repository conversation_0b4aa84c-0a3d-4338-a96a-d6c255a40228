"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Contexts = void 0;
const resource_1 = require("../resource.js");
class Contexts extends resource_1.APIResource {
    /**
     * Create a Context
     */
    create(body, options) {
        return this._client.post('/v1/contexts', { body, ...options });
    }
    /**
     * Context
     */
    retrieve(id, options) {
        return this._client.get(`/v1/contexts/${id}`, options);
    }
    /**
     * Update Context
     */
    update(id, options) {
        return this._client.put(`/v1/contexts/${id}`, options);
    }
}
exports.Contexts = Contexts;
//# sourceMappingURL=contexts.js.map
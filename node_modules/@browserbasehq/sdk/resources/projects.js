"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Projects = void 0;
const resource_1 = require("../resource.js");
class Projects extends resource_1.APIResource {
    /**
     * Project
     */
    retrieve(id, options) {
        return this._client.get(`/v1/projects/${id}`, options);
    }
    /**
     * List projects
     */
    list(options) {
        return this._client.get('/v1/projects', options);
    }
    /**
     * Project Usage
     */
    usage(id, options) {
        return this._client.get(`/v1/projects/${id}/usage`, options);
    }
}
exports.Projects = Projects;
//# sourceMappingURL=projects.js.map
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../resource.mjs";
export class Contexts extends APIResource {
    /**
     * Create a Context
     */
    create(body, options) {
        return this._client.post('/v1/contexts', { body, ...options });
    }
    /**
     * Context
     */
    retrieve(id, options) {
        return this._client.get(`/v1/contexts/${id}`, options);
    }
    /**
     * Update Context
     */
    update(id, options) {
        return this._client.put(`/v1/contexts/${id}`, options);
    }
}
//# sourceMappingURL=contexts.mjs.map
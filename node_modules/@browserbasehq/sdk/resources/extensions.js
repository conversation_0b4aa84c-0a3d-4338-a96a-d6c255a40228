"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Extensions = void 0;
const resource_1 = require("../resource.js");
const Core = __importStar(require("../core.js"));
class Extensions extends resource_1.APIResource {
    /**
     * Upload an Extension
     */
    create(body, options) {
        return this._client.post('/v1/extensions', Core.multipartFormRequestOptions({ body, ...options }));
    }
    /**
     * Extension
     */
    retrieve(id, options) {
        return this._client.get(`/v1/extensions/${id}`, options);
    }
    /**
     * Delete Extension
     */
    delete(id, options) {
        return this._client.delete(`/v1/extensions/${id}`, {
            ...options,
            headers: { Accept: '*/*', ...options?.headers },
        });
    }
}
exports.Extensions = Extensions;
//# sourceMappingURL=extensions.js.map
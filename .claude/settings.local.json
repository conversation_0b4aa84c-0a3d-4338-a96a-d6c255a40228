{"permissions": {"allow": ["Bash(docker compose restart:*)", "Bash(docker system:*)", "<PERSON><PERSON>(docker builder:*)", "Ba<PERSON>(docker image prune:*)", "<PERSON><PERSON>(docker rmi:*)", "Bash(find:*)", "Bash(ls:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(docker cp:*)", "Bash(docker compose logs:*)", "Bash(docker compose stop:*)", "<PERSON><PERSON>(docker compose:*)", "mcp__zen__thinkdeep", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(curl:*)", "mcp__zen__analyze", "<PERSON><PERSON>(python:*)", "Bash(grep:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker-compose restart:*)", "<PERSON><PERSON>(docker restart:*)", "<PERSON><PERSON>(docker-compose down:*)", "<PERSON><PERSON>(docker-compose up:*)", "<PERSON><PERSON>(docker-compose stop:*)", "Bash(docker-compose build:*)", "mcp__zen__chat", "<PERSON><PERSON>(docker-compose:*)", "WebFetch(domain:docs.stagehand.dev)", "<PERSON><PERSON>(cat:*)", "Bash(npm install)", "mcp__zen__codereview"], "deny": []}}
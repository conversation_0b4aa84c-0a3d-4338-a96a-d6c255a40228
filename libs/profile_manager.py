"""
Applicant Profile Manager
Handles loading and processing of applicant profile information
"""
import json
import os
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime


@dataclass
class ApplicantProfile:
    """Structured applicant profile data"""
    first_name: str
    last_name: str
    full_name: str
    email: str
    phone: str
    address: Dict[str, str]
    linkedin: Optional[str] = None
    portfolio: Optional[str] = None
    github: Optional[str] = None
    
    # Work authorization
    authorized_to_work: bool = True
    requires_sponsorship: bool = False
    visa_status: str = "US Citizen"
    
    # Experience
    years_of_experience: int = 0
    current_title: str = ""
    current_company: str = ""
    salary_min: int = 0
    salary_max: int = 0
    start_date: str = "2 weeks notice"
    remote_ok: bool = True
    relocation_ok: bool = False
    
    # Skills and education
    programming_languages: List[str] = None
    frameworks: List[str] = None
    technologies: List[str] = None
    degree: str = ""
    field: str = ""
    university: str = ""
    graduation_year: int = 0
    
    # Documents
    resume_path: str = ""
    cover_letter_template: str = ""
    
    def __post_init__(self):
        if self.programming_languages is None:
            self.programming_languages = []
        if self.frameworks is None:
            self.frameworks = []
        if self.technologies is None:
            self.technologies = []


class ProfileManager:
    """Manages applicant profile and form field mapping"""
    
    def __init__(self, profile_path: str = "profile.json"):
        self.profile_path = profile_path
        self.profile = self.load_profile()
        
    def load_profile(self) -> ApplicantProfile:
        """Load profile from JSON file"""
        if not os.path.exists(self.profile_path):
            raise FileNotFoundError(f"Profile file not found: {self.profile_path}")
            
        with open(self.profile_path, 'r') as f:
            data = json.load(f)
            
        # Extract and flatten profile data
        personal = data.get('personal_info', {})
        address = personal.get('address', {})
        work_auth = data.get('work_authorization', {})
        experience = data.get('experience', {})
        salary = experience.get('salary_expectation', {})
        availability = experience.get('availability', {})
        skills = data.get('skills', {})
        education = data.get('education', {})
        documents = data.get('documents', {})
        
        return ApplicantProfile(
            first_name=personal.get('first_name', ''),
            last_name=personal.get('last_name', ''),
            full_name=personal.get('full_name', ''),
            email=personal.get('email', ''),
            phone=personal.get('phone', ''),
            address=address,
            linkedin=personal.get('linkedin'),
            portfolio=personal.get('portfolio'),
            github=personal.get('github'),
            
            authorized_to_work=work_auth.get('authorized_to_work', True),
            requires_sponsorship=work_auth.get('requires_sponsorship', False),
            visa_status=work_auth.get('visa_status', 'US Citizen'),
            
            years_of_experience=experience.get('years_of_experience', 0),
            current_title=experience.get('current_title', ''),
            current_company=experience.get('current_company', ''),
            salary_min=salary.get('min', 0),
            salary_max=salary.get('max', 0),
            start_date=availability.get('start_date', '2 weeks notice'),
            remote_ok=availability.get('remote_ok', True),
            relocation_ok=availability.get('relocation_ok', False),
            
            programming_languages=skills.get('programming_languages', []),
            frameworks=skills.get('frameworks', []),
            technologies=skills.get('technologies', []),
            
            degree=education.get('degree', ''),
            field=education.get('field', ''),
            university=education.get('university', ''),
            graduation_year=education.get('graduation_year', 0),
            
            resume_path=documents.get('resume_path', ''),
            cover_letter_template=documents.get('cover_letter_template', '')
        )
    
    def get_form_field_mapping(self) -> Dict[str, str]:
        """Get common form field mappings for job applications"""
        p = self.profile
        
        # Common field variations and their values
        return {
            # Name fields
            "first name": p.first_name,
            "firstname": p.first_name,
            "fname": p.first_name,
            "given name": p.first_name,
            
            "last name": p.last_name,
            "lastname": p.last_name,
            "lname": p.last_name,
            "surname": p.last_name,
            "family name": p.last_name,
            
            "full name": p.full_name,
            "name": p.full_name,
            "your name": p.full_name,
            
            # Contact info
            "email": p.email,
            "email address": p.email,
            "e-mail": p.email,
            "work email": p.email,
            
            "phone": p.phone,
            "phone number": p.phone,
            "mobile": p.phone,
            "mobile number": p.phone,
            "cell phone": p.phone,
            "contact number": p.phone,
            
            # Address
            "address": p.address.get('street', ''),
            "street address": p.address.get('street', ''),
            "address line 1": p.address.get('street', ''),
            
            "city": p.address.get('city', ''),
            "state": p.address.get('state', ''),
            "zip": p.address.get('zip_code', ''),
            "zip code": p.address.get('zip_code', ''),
            "postal code": p.address.get('zip_code', ''),
            "country": p.address.get('country', ''),
            
            # Professional links
            "linkedin": p.linkedin or '',
            "linkedin profile": p.linkedin or '',
            "linkedin url": p.linkedin or '',
            
            "portfolio": p.portfolio or '',
            "portfolio url": p.portfolio or '',
            "website": p.portfolio or '',
            "personal website": p.portfolio or '',
            
            "github": p.github or '',
            "github profile": p.github or '',
            "github url": p.github or '',
            
            # Experience
            "years of experience": str(p.years_of_experience),
            "experience": str(p.years_of_experience),
            "years experience": str(p.years_of_experience),
            
            "current title": p.current_title,
            "job title": p.current_title,
            "position": p.current_title,
            "current position": p.current_title,
            
            "current company": p.current_company,
            "company": p.current_company,
            "employer": p.current_company,
            "current employer": p.current_company,
            
            # Salary
            "salary expectation": f"${p.salary_min:,} - ${p.salary_max:,}",
            "expected salary": f"${p.salary_min:,} - ${p.salary_max:,}",
            "salary range": f"${p.salary_min:,} - ${p.salary_max:,}",
            "desired salary": f"${p.salary_max:,}",
            
            # Availability
            "start date": p.start_date,
            "available start date": p.start_date,
            "when can you start": p.start_date,
            "availability": p.start_date,
            
            # Work authorization
            "authorized to work": "Yes" if p.authorized_to_work else "No",
            "work authorization": "Yes" if p.authorized_to_work else "No",
            "legal to work": "Yes" if p.authorized_to_work else "No",
            
            "require sponsorship": "Yes" if p.requires_sponsorship else "No",
            "visa sponsorship": "Yes" if p.requires_sponsorship else "No",
            "need sponsorship": "Yes" if p.requires_sponsorship else "No",
            
            "visa status": p.visa_status,
            
            # Remote work
            "remote work": "Yes" if p.remote_ok else "No",
            "work remotely": "Yes" if p.remote_ok else "No",
            "open to remote": "Yes" if p.remote_ok else "No",
            
            "willing to relocate": "Yes" if p.relocation_ok else "No",
            "relocation": "Yes" if p.relocation_ok else "No",
            "open to relocation": "Yes" if p.relocation_ok else "No",
            
            # Education
            "degree": p.degree,
            "education": f"{p.degree} in {p.field}",
            "highest degree": p.degree,
            
            "field of study": p.field,
            "major": p.field,
            "area of study": p.field,
            
            "university": p.university,
            "school": p.university,
            "college": p.university,
            
            "graduation year": str(p.graduation_year),
            "year graduated": str(p.graduation_year),
            
            # Skills (as comma-separated strings)
            "skills": ", ".join(p.programming_languages + p.frameworks + p.technologies),
            "technical skills": ", ".join(p.programming_languages + p.technologies),
            "programming languages": ", ".join(p.programming_languages),
            "technologies": ", ".join(p.technologies),
            "frameworks": ", ".join(p.frameworks)
        }
    
    def generate_cover_letter(self, company_name: str, job_title: str) -> str:
        """Generate a customized cover letter for the application"""
        if not os.path.exists(self.profile.cover_letter_template):
            return f"I am interested in the {job_title} position at {company_name}."
            
        with open(self.profile.cover_letter_template, 'r') as f:
            template = f.read()
            
        # Replace template variables
        replacements = {
            '{job_title}': job_title,
            '{company_name}': company_name,
            '{years_experience}': str(self.profile.years_of_experience),
            '{current_title}': self.profile.current_title,
            '{current_company}': self.profile.current_company,
            '{key_skills}': ", ".join(self.profile.programming_languages[:3]),
            '{availability}': self.profile.start_date,
            '{full_name}': self.profile.full_name,
            '{email}': self.profile.email,
            '{phone}': self.profile.phone
        }
        
        for placeholder, value in replacements.items():
            template = template.replace(placeholder, value)
            
        return template
    
    def get_resume_path(self) -> str:
        """Get the path to the resume file"""
        return self.profile.resume_path
    
    def should_apply_to_job(self, job_data: Dict[str, Any]) -> bool:
        """Determine if we should apply to this job based on preferences"""
        # Load application settings from profile
        with open(self.profile_path, 'r') as f:
            data = json.load(f)
        
        settings = data.get('application_settings', {})
        blacklist = settings.get('blacklisted_companies', [])
        
        company = job_data.get('company', '').lower()
        
        # Check blacklist
        for blocked in blacklist:
            if blocked.lower() in company:
                return False
                
        return True
    
    def validate_profile(self) -> List[str]:
        """Validate profile completeness and return list of missing required fields"""
        errors = []
        
        required_fields = [
            ('first_name', 'First name'),
            ('last_name', 'Last name'), 
            ('email', 'Email address'),
            ('phone', 'Phone number')
        ]
        
        for field, display_name in required_fields:
            if not getattr(self.profile, field):
                errors.append(f"Missing required field: {display_name}")
                
        # Check if resume file exists
        if self.profile.resume_path and not os.path.exists(self.profile.resume_path):
            errors.append(f"Resume file not found: {self.profile.resume_path}")
            
        return errors
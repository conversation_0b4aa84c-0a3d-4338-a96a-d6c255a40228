#!/usr/bin/env python3
"""
Email Manager for Job Application Automation
Handles Zoho Mail integration for sign-ups, verification, and communication
"""

import imaplib
import smtplib
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import decode_header
import ssl
import time
import re
import random
import string
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import json
import os
from dataclasses import dataclass
import logging

@dataclass
class EmailConfig:
    """Email configuration from environment variables"""
    imap_host: str
    imap_port: int
    imap_user: str
    imap_pass: str
    imap_ssl: bool
    
    smtp_host: str
    smtp_port: int
    smtp_user: str
    smtp_pass: str
    smtp_ssl: bool
    smtp_tls: bool
    
    email_domain: str
    daily_cap: int = 20
    
    @classmethod
    def from_env(cls):
        """Load configuration from environment variables"""
        return cls(
            imap_host=os.getenv('ZOHO_IMAP_HOST', 'imappro.zoho.in'),
            imap_port=int(os.getenv('ZOHO_IMAP_PORT', '993')),
            imap_user=os.getenv('ZOHO_IMAP_USER'),
            imap_pass=os.getenv('ZOHO_IMAP_PASS'),
            imap_ssl=os.getenv('ZOHO_IMAP_SSL', 'true').lower() == 'true',
            
            smtp_host=os.getenv('ZOHO_SMTP_HOST', 'smtppro.zoho.in'),
            smtp_port=int(os.getenv('ZOHO_SMTP_PORT', '465')),
            smtp_user=os.getenv('ZOHO_SMTP_USER'),
            smtp_pass=os.getenv('ZOHO_SMTP_PASS'),
            smtp_ssl=os.getenv('ZOHO_SMTP_SSL', 'true').lower() == 'true',
            smtp_tls=os.getenv('ZOHO_SMTP_TLS', 'false').lower() == 'true',
            
            email_domain=os.getenv('EMAIL_DOMAIN', 'karmsakha.com'),
            daily_cap=int(os.getenv('EMAIL_DAILY_CAP', '20'))
        )

@dataclass
class EmailAlias:
    """Email alias for job applications"""
    alias: str
    company: str
    job_title: str
    platform: str
    created_at: datetime
    used_for_signup: bool = False
    verified: bool = False
    notes: str = ""

class EmailManager:
    """Manages email operations for job applications"""
    
    def __init__(self, config: EmailConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.aliases: Dict[str, EmailAlias] = {}
        self.daily_email_count = 0
        self.last_reset_date = datetime.now().date()
        
    def generate_alias(self, company: str, job_title: str, platform: str) -> str:
        """
        Generate a legitimate-looking email alias for a job application

        Creates natural variations like:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        """
        # Use Sakshi's actual name for legitimate appearance
        first_name = "sakshi"
        last_name = "shah"

        # Generate different legitimate patterns
        patterns = [
            f"{first_name}.{last_name}",
            f"{first_name}.{last_name}.legal",
            f"{first_name}.{last_name}.law",
            f"{first_name[0]}.{last_name}",
            f"{first_name[0]}.{last_name}.legal",
            f"{first_name}.{last_name[0]}",
            f"{first_name}.{last_name}.lawyer",
            f"{first_name}.{last_name}2024",
            f"{first_name}.{last_name}.advocate",
            f"{first_name[0]}{last_name}",
            f"{first_name}{last_name[0]}",
            f"{first_name}.legal",
            f"{first_name}.law.{last_name}",
        ]

        # Add some variation based on company/role
        if "legal" in job_title.lower() or "lawyer" in job_title.lower():
            patterns.extend([
                f"{first_name}.{last_name}.counsel",
                f"{first_name}.legal.{last_name}",
            ])

        # Select a pattern and add small random element if needed
        import hashlib
        company_hash = hashlib.md5(company.lower().encode()).hexdigest()[:2]
        pattern_index = int(company_hash, 16) % len(patterns)
        selected_pattern = patterns[pattern_index]

        # Add small number if pattern might be common
        if len(selected_pattern.split('.')) <= 2:
            year_suffix = random.choice(['', '2024', '24', str(random.randint(1, 99))])
            if year_suffix:
                selected_pattern += year_suffix

        # Create final alias
        alias = f"{selected_pattern}@{self.config.email_domain}"

        # Store alias information
        self.aliases[alias] = EmailAlias(
            alias=alias,
            company=company,
            job_title=job_title,
            platform=platform,
            created_at=datetime.now()
        )

        self.logger.info(f"Generated legitimate email alias: {alias}")
        return alias
    
    def connect_imap(self) -> imaplib.IMAP4_SSL:
        """Connect to IMAP server"""
        if self.config.imap_ssl:
            mail = imaplib.IMAP4_SSL(self.config.imap_host, self.config.imap_port)
        else:
            mail = imaplib.IMAP4(self.config.imap_host, self.config.imap_port)
        
        mail.login(self.config.imap_user, self.config.imap_pass)
        return mail
    
    def check_verification_emails(self, alias: str, timeout: int = 300) -> Optional[Dict[str, str]]:
        """
        Check for verification emails sent to an alias
        
        Args:
            alias: Email alias to check
            timeout: Maximum time to wait for email (seconds)
            
        Returns:
            Verification details if found
        """
        start_time = time.time()
        mail = None
        check_interval = 5  # Check every 5 seconds
        
        try:
            while time.time() - start_time < timeout:
                mail = self.connect_imap()
                mail.select('INBOX')
                
                # Search for emails to the alias
                # Zoho treats aliases as the main email, so we search more broadly
                alias_part = alias.split("@")[0]
                
                # Try multiple search strategies
                search_strategies = [
                    'ALL',  # Get all emails first
                    f'TO "{alias}"',  # Direct alias match
                    f'SUBJECT "verification"',  # Subject contains verification
                    f'SUBJECT "Test Verification"',  # Our specific test subject
                ]
                
                email_ids = []
                for search_criteria in search_strategies:
                    try:
                        typ, data = mail.search(None, search_criteria)
                        if typ == 'OK' and data[0]:
                            email_ids.extend(data[0].split())
                            break  # Use first successful search
                    except Exception as search_error:
                        self.logger.debug(f"Search strategy '{search_criteria}' failed: {search_error}")
                        continue
                
                if email_ids:
                    # Remove duplicates and check recent emails
                    unique_email_ids = list(set(email_ids))
                    
                    for email_id in reversed(unique_email_ids[-10:]):  # Check last 10 emails
                        typ, msg_data = mail.fetch(email_id, '(RFC822)')
                        
                        if typ == 'OK':
                            raw_email = msg_data[0][1]
                            msg = email.message_from_bytes(raw_email)
                            
                            # Extract verification link
                            verification_data = self._extract_verification_link(msg)
                            if verification_data:
                                self.aliases[alias].verified = True
                                mail.close()
                                mail.logout()
                                return verification_data
                
                mail.close()
                mail.logout()
                
                # Check if we should continue waiting
                elapsed = time.time() - start_time
                if elapsed >= timeout:
                    break
                    
                # Wait before checking again, but don't exceed timeout
                sleep_time = min(check_interval, timeout - elapsed)
                if sleep_time > 0:
                    time.sleep(sleep_time)
                
        except Exception as e:
            self.logger.error(f"Error checking verification emails: {e}")
            if mail:
                try:
                    mail.close()
                    mail.logout()
                except:
                    pass
        
        return None
    
    def _extract_verification_link(self, msg: email.message.Message) -> Optional[Dict[str, str]]:
        """Extract verification link and code from email"""
        result = {
            'subject': '',
            'from': '',
            'verification_link': None,
            'verification_code': None,
            'body_text': ''
        }
        
        # Get subject - handle case where Subject might be None
        subject_header = msg.get('Subject', '')
        if subject_header:
            try:
                subject, encoding = decode_header(subject_header)[0]
                if isinstance(subject, bytes):
                    subject = subject.decode(encoding or 'utf-8')
                result['subject'] = subject
            except Exception:
                result['subject'] = str(subject_header)
        
        result['from'] = msg.get('From', '')
        
        # Check if this is a verification email
        verification_keywords = ['verify', 'confirm', 'activate', 'validation', 'complete your registration']
        subject_lower = result['subject'].lower()
        if not any(keyword in subject_lower for keyword in verification_keywords):
            # Check body
            body_text = self._get_email_body(msg)
            if not any(keyword in body_text.lower() for keyword in verification_keywords):
                return None
        
        # Extract body
        body_text = self._get_email_body(msg)
        result['body_text'] = body_text
        
        # Find verification link
        url_patterns = [
            r'https?://[^\s<>"{}|\\^`\[\]]+(?:verify|confirm|activate|validate)[^\s<>"{}|\\^`\[\]]*',
            r'https?://[^\s<>"{}|\\^`\[\]]+/[^\s<>"{}|\\^`\[\]]*(?:token|key|code)[^\s<>"{}|\\^`\[\]]*',
            r'https?://[^\s<>"{}|\\^`\[\]]+',  # Any URL as fallback
        ]
        
        for pattern in url_patterns:
            urls = re.findall(pattern, body_text, re.IGNORECASE)
            if urls:
                result['verification_link'] = urls[0]
                break
        
        # Find verification code (6-8 digit codes or alphanumeric codes)
        code_patterns = [
            r'code:\s*([A-Z0-9]{6,9})',  # Code: XXXXX format
            r'verification code is:\s*([A-Z0-9]{6,9})',  # Verification code is: XXXXX
            r'verification code:\s*([A-Z0-9]{6,9})',  # Verification code: XXXXX
            r'enter this code:\s*([A-Z0-9]{6,9})',  # Enter this code: XXXXX
            r'code is:\s*([A-Z0-9]{6,9})',  # Code is: XXXXX
            r'\b([A-Z]{2,8}\d{3,6}[A-Z0-9]*)\b',  # Alphanumeric like INDEED789
            r'\b(\d{6,8})\b',  # 6-8 digit codes standalone
        ]
        
        for pattern in code_patterns:
            codes = re.findall(pattern, body_text, re.IGNORECASE)
            if codes:
                result['verification_code'] = codes[0]
                break
        
        return result if (result['verification_link'] or result['verification_code']) else None
    
    def _get_email_body(self, msg: email.message.Message) -> str:
        """Extract email body text"""
        body = ""
        
        if msg.is_multipart():
            for part in msg.walk():
                if part.get_content_type() == "text/plain":
                    try:
                        body += part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    except:
                        pass
                elif part.get_content_type() == "text/html":
                    try:
                        html_body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                        # Simple HTML to text conversion
                        body += re.sub('<[^<]+?>', '', html_body)
                    except:
                        pass
        else:
            try:
                body = msg.get_payload(decode=True).decode('utf-8', errors='ignore')
            except:
                body = str(msg.get_payload())
        
        return body
    
    def send_email(self, to_email: str, subject: str, body: str, 
                   from_alias: Optional[str] = None) -> bool:
        """
        Send email using SMTP
        
        Args:
            to_email: Recipient email
            subject: Email subject
            body: Email body
            from_alias: Optional from alias
            
        Returns:
            Success status
        """
        # Check daily cap
        if self._check_reset_daily_counter():
            self.daily_email_count = 0
            
        if self.daily_email_count >= self.config.daily_cap:
            self.logger.warning(f"Daily email cap ({self.config.daily_cap}) reached")
            return False
        
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = from_alias or self.config.smtp_user
            msg['To'] = to_email
            msg['Subject'] = subject
            msg.attach(MIMEText(body, 'plain'))
            
            # Connect to SMTP
            if self.config.smtp_ssl:
                server = smtplib.SMTP_SSL(self.config.smtp_host, self.config.smtp_port)
            else:
                server = smtplib.SMTP(self.config.smtp_host, self.config.smtp_port)
                if self.config.smtp_tls:
                    server.starttls()
            
            server.login(self.config.smtp_user, self.config.smtp_pass)
            server.send_message(msg)
            server.quit()
            
            self.daily_email_count += 1
            self.logger.info(f"Email sent successfully to {to_email}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send email: {e}")
            return False
    
    def _check_reset_daily_counter(self) -> bool:
        """Check if daily counter should be reset"""
        current_date = datetime.now().date()
        if current_date > self.last_reset_date:
            self.last_reset_date = current_date
            return True
        return False
    
    def get_form_data_for_alias(self, alias: str) -> Dict[str, Any]:
        """
        Get form filling data for a specific alias using Sakshi's profile

        Returns:
            Dictionary with form field mappings
        """
        if alias not in self.aliases:
            return {}

        alias_info = self.aliases[alias]

        # Use Sakshi's actual profile information
        form_data = {
            'email': alias,
            'company': alias_info.company,
            'position': alias_info.job_title,
            'platform': alias_info.platform,
            'first_name': 'Sakshi',
            'last_name': 'Shah',
            'full_name': 'Sakshi Shah',
            'phone': '+91 8469485836',
            'city': 'New Delhi',
            'state': 'Delhi',
            'country': 'India',
            'verified': alias_info.verified
        }

        return form_data
    
    def monitor_responses(self, alias: str, check_interval: int = 3600) -> List[Dict[str, Any]]:
        """
        Monitor responses to a specific alias
        
        Args:
            alias: Email alias to monitor
            check_interval: How often to check (seconds)
            
        Returns:
            List of responses received
        """
        responses = []
        
        try:
            mail = self.connect_imap()
            mail.select('INBOX')
            
            # Search for responses with multiple strategies
            search_strategies = [
                'ALL',  # Get all emails
                'SUBJECT "Re:"',  # Replies
                'SUBJECT "Interview"',  # Interview emails
                'SUBJECT "Application"'  # Application responses
            ]
            
            email_ids = []
            for search_criteria in search_strategies:
                try:
                    typ, data = mail.search(None, search_criteria)
                    if typ == 'OK' and data[0]:
                        email_ids.extend(data[0].split())
                        break
                except Exception:
                    continue
            
            if email_ids:
                unique_email_ids = list(set(email_ids))
                
                for email_id in unique_email_ids:
                    typ, msg_data = mail.fetch(email_id, '(RFC822)')
                    
                    if typ == 'OK':
                        raw_email = msg_data[0][1]
                        msg = email.message_from_bytes(raw_email)
                        
                        response = {
                            'subject': '',
                            'from': msg['From'],
                            'date': msg['Date'],
                            'body': self._get_email_body(msg),
                            'is_interview': False,
                            'is_rejection': False
                        }
                        
                        # Get subject
                        subject, encoding = decode_header(msg['Subject'])[0]
                        if isinstance(subject, bytes):
                            subject = subject.decode(encoding or 'utf-8')
                        response['subject'] = subject
                        
                        # Categorize response
                        interview_keywords = ['interview', 'meeting', 'call', 'schedule', 'availability']
                        rejection_keywords = ['unfortunately', 'not selected', 'other candidates', 'not moving forward']
                        
                        body_lower = response['body'].lower()
                        subject_lower = subject.lower()
                        
                        if any(keyword in body_lower or keyword in subject_lower for keyword in interview_keywords):
                            response['is_interview'] = True
                        elif any(keyword in body_lower or keyword in subject_lower for keyword in rejection_keywords):
                            response['is_rejection'] = True
                        
                        responses.append(response)
            
            mail.close()
            mail.logout()
            
        except Exception as e:
            self.logger.error(f"Error monitoring responses: {e}")
        
        return responses
    
    def save_aliases(self, filepath: str = "email_aliases.json"):
        """Save aliases to file"""
        data = {}
        for alias, info in self.aliases.items():
            data[alias] = {
                'company': info.company,
                'job_title': info.job_title,
                'platform': info.platform,
                'created_at': info.created_at.isoformat(),
                'used_for_signup': info.used_for_signup,
                'verified': info.verified,
                'notes': info.notes
            }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
    
    def load_aliases(self, filepath: str = "email_aliases.json"):
        """Load aliases from file"""
        if os.path.exists(filepath):
            with open(filepath, 'r') as f:
                data = json.load(f)
                
            for alias, info in data.items():
                self.aliases[alias] = EmailAlias(
                    alias=alias,
                    company=info['company'],
                    job_title=info['job_title'],
                    platform=info['platform'],
                    created_at=datetime.fromisoformat(info['created_at']),
                    used_for_signup=info['used_for_signup'],
                    verified=info['verified'],
                    notes=info['notes']
                )


# Example usage
def example_usage():
    """Example of how to use EmailManager"""
    
    # Load config from environment
    config = EmailConfig.from_env()
    
    # Create email manager
    email_mgr = EmailManager(config)
    
    # Generate alias for a job application
    alias = email_mgr.generate_alias(
        company="Google",
        job_title="Software Engineer",
        platform="LinkedIn"
    )
    print(f"Generated alias: {alias}")
    
    # Use alias for signup
    # ... (signup process)
    
    # Check for verification email
    print("Checking for verification email...")
    verification = email_mgr.check_verification_emails(alias, timeout=120)
    
    if verification:
        print(f"Verification link: {verification['verification_link']}")
        print(f"Verification code: {verification['verification_code']}")
    
    # Get form data
    form_data = email_mgr.get_form_data_for_alias(alias)
    print(f"Form data: {form_data}")
    
    # Monitor responses
    responses = email_mgr.monitor_responses(alias)
    for response in responses:
        if response['is_interview']:
            print(f"Interview invitation from {response['from']}")
        elif response['is_rejection']:
            print(f"Rejection from {response['from']}")
    
    # Save aliases
    email_mgr.save_aliases()

if __name__ == "__main__":
    example_usage()